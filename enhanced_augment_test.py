"""
增强版Augment自动化测试
集成idea命令行启动功能
"""

import time
import pyautogui
import pyperclip
import subprocess
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_idea_window():
    """
    查找IntelliJ IDEA窗口 - 改进的检测逻辑
    """
    try:
        import pygetwindow as gw

        # 获取所有窗口
        all_windows = gw.getAllWindows()

        # 查找包含IDEA相关关键词的窗口
        idea_keywords = [
            'IntelliJ IDEA',
            'IDEA',
            'JetBrains',
            '.java',
            '.py',
            '.js',
            '.kt'
        ]

        for window in all_windows:
            if window.title and len(window.title) > 5:  # 过滤掉空标题和太短的标题
                title_lower = window.title.lower()

                # 检查是否包含IDEA相关关键词
                for keyword in idea_keywords:
                    if keyword.lower() in title_lower:
                        # 进一步验证是否是IDEA窗口
                        if ('intellij' in title_lower or
                            'idea' in title_lower or
                            'jetbrains' in title_lower or
                            any(ext in title_lower for ext in ['.java', '.py', '.js', '.kt', '.xml'])):
                            print(f"🔍 找到可能的IDEA窗口: {window.title}")
                            return window

        # 如果上面的方法没找到，尝试更宽泛的搜索
        for window in all_windows:
            if window.title and len(window.title) > 10:
                # 检查窗口大小，IDEA通常是较大的窗口
                if window.width > 800 and window.height > 600:
                    title_lower = window.title.lower()
                    # 检查是否包含项目路径或常见的开发相关词汇
                    if any(word in title_lower for word in ['project', 'src', 'main', 'java', 'python']):
                        print(f"🔍 找到可能的开发工具窗口: {window.title}")
                        return window

        return None

    except ImportError:
        # 如果没有pygetwindow，回退到原来的方法
        print("⚠️ pygetwindow未安装，使用基础检测方法")
        windows = pyautogui.getWindowsWithTitle('IntelliJ IDEA')
        return windows[0] if windows else None
    except Exception as e:
        print(f"⚠️ 窗口检测异常: {e}")
        return None

def enhanced_augment_test(project_path=None):
    """
    增强版Augment测试，支持命令行启动IDEA
    """
    print("=== 增强版Augment自动化测试 ===")
    
    # 默认项目路径
    if not project_path:
        project_path = input("请输入项目路径 (默认: D:/project/AI-assisted): ").strip()
        if not project_path:
            project_path = "D:/project/AI-assisted"
    
    try:
        # 1. 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 1
        print("✅ 初始化完成")
        
        # 2. 使用idea命令启动IDEA
        print(f"🚀 启动IntelliJ IDEA并打开项目: {project_path}")
        
        if os.path.exists(project_path):
            try:
                # 使用idea命令打开项目
                print("📂 执行命令: idea " + project_path)
                subprocess.Popen(['idea', project_path], shell=True)
                print("✅ IDEA启动命令已执行")
                
                # 等待IDEA启动
                print("⏳ 等待IntelliJ IDEA启动 (20秒)...")
                for i in range(20):
                    print(f"启动中... {i+1}/20秒")
                    time.sleep(1)
                
                # 检查IDEA是否启动成功 - 改进的检测逻辑
                idea_window = find_idea_window()
                if idea_window:
                    print(f"✅ 检测到IntelliJ IDEA窗口: {idea_window.title}")
                    idea_window.activate()
                    time.sleep(3)
                else:
                    print("⚠️ 未检测到IDEA窗口，可能还在启动中")
                    print("正在重新检测...")

                    # 额外等待和重新检测
                    for retry in range(3):
                        time.sleep(5)
                        idea_window = find_idea_window()
                        if idea_window:
                            print(f"✅ 检测到IntelliJ IDEA窗口: {idea_window.title}")
                            idea_window.activate()
                            time.sleep(3)
                            break
                        print(f"重试 {retry + 1}/3...")
                    else:
                        print("❌ 仍未检测到IDEA窗口")
                        input("请等待IDEA完全启动并加载项目后，按Enter继续...")
                    
            except Exception as e:
                print(f"❌ 使用idea命令启动失败: {e}")
                print("⚠️ 请手动打开IntelliJ IDEA")
                input("手动打开IDEA并加载项目后，按Enter继续...")
        else:
            print(f"❌ 项目路径不存在: {project_path}")
            input("请手动打开IntelliJ IDEA并加载正确的项目，然后按Enter继续...")
        
        # 3. 启动Augment Chat - 使用与simple_augment_direct.py相同的逻辑
        print("🤖 启动Augment Chat插件...")
        print("使用Action搜索打开Augment...")

        # 使用Action搜索
        pyautogui.hotkey('ctrl', 'shift', 'a')
        time.sleep(2)

        # 清空搜索框并输入Augment Chat
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.write("Augment Chat", interval=0.1)
        time.sleep(2)

        # 按Enter打开Augment
        pyautogui.press('enter')
        time.sleep(3)

        print("✅ Augment Chat启动命令已执行")

        # 等待加载完成
        print("⏳ 等待Augment Chat加载完成...")
        time.sleep(3)
        print("✅ 假设Augment Chat已成功打开，光标应该在输入框中")
        
        # 4. 发送分析请求
        print("📝 准备发送项目分析请求...")
        
        # 构造详细的分析提示
        analysis_prompt = f"""请对项目 {os.path.basename(project_path)} 进行全面的代码分析，包括：

🏗️ **架构分析**
- 项目整体架构设计
- 模块划分和依赖关系
- 设计模式的使用

📊 **代码质量评估**
- 代码规范性
- 可维护性评估
- 潜在的代码异味

🔧 **技术栈分析**
- 使用的编程语言和框架
- 第三方库和依赖
- 技术选型的合理性

⚡ **性能分析**
- 潜在的性能瓶颈
- 资源使用情况
- 优化建议

🔒 **安全性审查**
- 安全漏洞识别
- 数据处理安全性
- 安全最佳实践建议

📈 **改进建议**
- 代码重构建议
- 功能扩展方向
- 最佳实践推荐

请提供详细的分析报告，包含具体的代码示例和改进方案。"""
        
        # 清空剪贴板并复制提示
        pyperclip.copy(analysis_prompt)
        print("✅ 分析提示已复制到剪贴板")
        
        # 使用直接输入方式 - 不使用坐标点击
        success = send_message_directly(analysis_prompt)

        if not success:
            print("❌ 自动输入失败，请手动操作")
            print("请将剪贴板中的内容粘贴到Augment聊天框并发送")
            input("消息发送完成后，按Enter继续...")
        
        # 5. 智能等待响应
        print("⏳ 等待Augment分析完成...")
        
        wait_for_response()
        
        # 6. 获取分析结果
        print("📋 获取分析结果...")
        
        result = get_analysis_result()
        
        # 7. 保存结果
        if result:
            save_analysis_result(result, project_path)
        
        # 8. 保存截图
        save_screenshot()
        
        print("🎉 增强版测试完成！")
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def send_message_directly(text):
    """
    直接发送消息到Augment - 不使用坐标点击
    假设光标已经在输入框中
    """
    try:
        print("🎯 直接输入模式 - 不使用坐标点击")
        print("假设光标已经在Augment输入框中...")

        # 清空输入框（如果有内容）
        print("🧹 清空输入框...")
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)

        # 直接粘贴分析请求（文本已经在剪贴板中）
        print("📋 粘贴分析请求...")
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(2)

        # 发送消息
        print("🚀 发送消息...")
        pyautogui.press('enter')
        time.sleep(2)

        print("✅ 分析请求已发送")

        # 假设发送成功，不再询问用户确认
        print("✅ 假设消息发送成功，继续后续流程")
        return True

    except Exception as e:
        print(f"❌ 直接输入失败: {e}")
        return False

def wait_for_response():
    """
    智能等待响应 - 改进版
    """
    print("⏳ 等待Augment分析完成...")
    print("请观察Augment界面，等待分析结果出现")

    # 简化的等待策略
    wait_intervals = [60, 60, 60, 60, 60]  # 每分钟检查一次，最多5分钟

    for i, interval in enumerate(wait_intervals):
        print(f"⏰ 等待阶段 {i+1}/{len(wait_intervals)} ({interval}秒)")

        # 倒计时显示
        for remaining in range(interval, 0, -10):
            print(f"   剩余 {remaining} 秒...", end='\r')
            time.sleep(10)

        print()  # 换行

        # 询问用户
        try:
            response = input("分析完成了吗？(y=完成, n=继续等待, Enter=继续等待): ").strip().lower()
            if response == 'y':
                print("✅ 用户确认分析完成")
                break
            elif response == 'n':
                print("⏳ 继续等待...")
                continue
            else:
                print("⏳ 继续等待...")
        except:
            print("⏳ 继续等待...")

    print("✅ 等待阶段完成")

def get_analysis_result():
    """
    获取分析结果
    """
    try:
        print("📋 尝试获取分析结果...")
        
        # 尝试全选并复制
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)
        
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)
        
        # 获取剪贴板内容
        result = pyperclip.paste()
        
        if result and len(result) > 50:
            print(f"✅ 成功获取分析结果 ({len(result)} 字符)")
            return result
        else:
            print("❌ 未能获取到有效结果")
            print("请手动复制Augment的分析结果")
            input("复制完成后按Enter继续...")
            
            # 再次尝试获取
            result = pyperclip.paste()
            return result if result and len(result) > 50 else None
            
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_analysis_result(result, project_path):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        project_name = os.path.basename(project_path)
        filename = f"augment_analysis_{project_name}_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment项目分析报告 ===\n")
            f.write(f"项目: {project_path}\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"工具版本: 增强版 v1.0\n")
            f.write("=" * 60 + "\n\n")
            f.write(result)
        
        print(f"📁 分析结果已保存到: {filename}")
        
        # 显示结果预览
        print("\n📄 分析结果预览:")
        print("-" * 50)
        preview = result[:500] + "..." if len(result) > 500 else result
        print(preview)
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def save_screenshot():
    """
    保存截图
    """
    try:
        timestamp = int(time.time())
        screenshot_filename = f"augment_screenshot_{timestamp}.png"
        
        screenshot = pyautogui.screenshot()
        screenshot.save(screenshot_filename)
        
        print(f"📸 截图已保存到: {screenshot_filename}")
        
    except Exception as e:
        print(f"❌ 保存截图失败: {e}")

def main():
    """
    主函数
    """
    print("=== 增强版Augment自动化测试工具 v2.0 ===")
    print("✨ 新功能:")
    print("- 支持使用idea命令自动启动IDE")
    print("- 使用'Augment Chat'命令正确打开聊天界面")
    print("- 直接输入模式，不使用坐标点击")
    print("- 改进的等待和确认机制")
    print()
    
    # 获取项目路径
    print("请选择要分析的项目:")
    print("1. AI-assisted (D:/project/AI-assisted)")
    print("2. gate_manzhouli_new (D:/project/gate_manzhouli_new)")
    print("3. 自定义路径")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        project_path = "D:/project/AI-assisted"
    elif choice == "2":
        project_path = "D:/project/gate_manzhouli_new"
    elif choice == "3":
        project_path = input("请输入项目完整路径: ").strip()
    else:
        print("无效选择，使用默认路径")
        project_path = "D:/project/AI-assisted"
    
    print(f"📂 选择的项目路径: {project_path}")
    
    if not os.path.exists(project_path):
        print(f"❌ 路径不存在: {project_path}")
        return
    
    # 开始测试
    success = enhanced_augment_test(project_path)
    
    if success:
        print("\n🎉 测试成功完成！")
        print("生成的文件:")
        print("- augment_analysis_*.txt (分析结果)")
        print("- augment_screenshot_*.png (截图)")
    else:
        print("\n❌ 测试未能完成")

if __name__ == "__main__":
    main()
