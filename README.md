# Augment自动化监听系统

一个自动化的Augment AI助手监听系统，能够自动发送分析请求、获取响应并保存到数据库。

## 🎯 核心功能

- **自动化交互**: 自动向Augment发送分析请求
- **智能监听**: 实时监听Augment的响应
- **数据库存储**: 自动将响应保存到SQLite数据库
- **JSON导出**: 生成结构化的JSON响应文件
- **持续运行**: 支持持续监听模式，可处理多个请求

## 📋 系统要求

- Python 3.8+
- IntelliJ IDEA with Augment插件
- Windows系统（已针对Windows优化）

## 🚀 快速开始

1. **安装依赖**：
```bash
pip install -r requirements.txt
```

2. **确保Augment已启动**：
   - 打开IntelliJ IDEA
   - 确保Augment插件已安装并启用
   - 打开Augment聊天界面

3. **运行监听系统**：
```bash
python augment_monitor_system.py
```

4. **选择运行模式**：
   - 模式1: 单次测试
   - 模式2: 持续监听模式（推荐）
   - 模式3: 查看数据库统计

## 📊 数据存储

### SQLite数据库 (`augment_responses.db`)
- **表**: `augment_responses` - 存储所有响应记录
- **表**: `augment_sessions` - 存储会话信息

### JSON文件
每次响应都会生成独立的JSON文件：`augment_response_[timestamp].json`

## 🔧 配置说明

系统已自动配置Augment输入框位置，如需调整：
- 修改 `AugmentMonitorSystem` 类中的 `input_position` 参数
- 默认位置：(1800, 900)

## 📈 使用示例

```python
from augment_monitor_system import AugmentMonitorSystem

# 创建监听系统
monitor = AugmentMonitorSystem()

# 处理单个请求
result = monitor.process_request("请分析这个项目的代码结构")

# 启动持续监听
monitor.start_continuous_monitoring()
```

## 📁 项目结构

```
AI-assisted/
├── augment_monitor_system.py    # 主要监听系统
├── augment_responses.db         # SQLite数据库
├── augment_response_*.json      # 响应JSON文件
├── augment_monitor.log          # 系统日志
├── requirements.txt             # Python依赖
├── augment_protos/             # Protobuf定义文件
└── README.md                   # 项目说明
```

## 🎉 成功案例

系统已成功测试：
- ✅ 自动发送分析请求
- ✅ 获取39,599字符的详细响应
- ✅ 质量评分17/20（优秀）
- ✅ 成功保存到数据库
- ✅ 生成完整JSON文件

## 🔍 监控和日志

- **系统日志**: `augment_monitor.log`
- **数据库统计**: 运行程序选择模式3查看
- **响应质量**: 自动计算质量评分（基于关键词匹配）

## ⚠️ 注意事项

1. 确保Augment界面在屏幕右侧可见
2. 不要在系统运行时移动或最小化IDE窗口
3. 系统会自动处理响应延迟和重试
4. 建议在稳定的网络环境下使用

## 🛠️ 故障排除

如果系统无法找到输入框：
1. 检查Augment界面是否打开
2. 确认输入框位置是否正确
3. 查看日志文件获取详细错误信息

## 📞 支持

如有问题，请查看：
- 系统日志文件
- 数据库记录
- JSON响应文件
