#!/usr/bin/env python3
"""
AI辅助项目JSON格式业务分析演示脚本
"""

import sys
import os
from datetime import datetime

def show_json_analysis_info():
    """
    显示JSON分析系统信息
    """
    print("=" * 70)
    print("🚀 AI辅助项目JSON格式业务分析系统")
    print("=" * 70)
    print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🎯 特殊功能: 要求Augment输出结构化JSON数据")
    print()

def show_json_requirements():
    """
    显示JSON输出要求
    """
    print("📋 JSON输出要求:")
    print("   🔹 要求Augment将分析结果组织成结构化JSON格式")
    print("   🔹 包含完整的项目分析数据结构")
    print("   🔹 涵盖7个主要分析维度")
    print("   🔹 提供可解析的JSON数据")
    print()
    
    print("📊 JSON数据结构:")
    print("   ├── project_analysis")
    print("   │   ├── project_overview (项目概述)")
    print("   │   ├── technical_architecture (技术架构)")
    print("   │   ├── business_process (业务流程)")
    print("   │   ├── feature_assessment (功能评估)")
    print("   │   ├── code_quality (代码质量)")
    print("   │   ├── improvement_suggestions (改进建议)")
    print("   │   └── technical_debt_risks (技术债务)")
    print("   └── analysis_metadata (分析元数据)")
    print()

def show_json_prompt_preview():
    """
    显示JSON提示词预览
    """
    try:
        from augment_monitor_system import AugmentMonitorSystem
        
        print("📝 JSON业务分析提示词预览:")
        print("-" * 50)
        
        monitor = AugmentMonitorSystem()
        prompt = monitor.business_analysis_prompt
        
        # 显示关键部分
        lines = prompt.split('\n')
        key_lines = []
        for line in lines[:15]:  # 前15行
            key_lines.append(line)
        
        for line in key_lines:
            print(line)
        
        print("...")
        print("(完整提示词包含详细的JSON结构要求)")
        print("-" * 50)
        print(f"📏 完整提示词长度: {len(prompt)} 字符")
        print()
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入系统模块: {e}")
        return False
    except Exception as e:
        print(f"❌ 预览失败: {e}")
        return False

def show_json_output_benefits():
    """
    显示JSON输出的优势
    """
    print("🎯 JSON格式输出的优势:")
    print("   ✅ 结构化数据 - 便于程序处理和分析")
    print("   ✅ 标准格式 - 易于集成到其他系统")
    print("   ✅ 可解析性 - 支持自动化数据提取")
    print("   ✅ 一致性 - 每次分析都有相同的数据结构")
    print("   ✅ 可扩展性 - 易于添加新的分析维度")
    print("   ✅ 数据库友好 - 便于存储和查询")
    print()

def show_usage_instructions():
    """
    显示使用说明
    """
    print("🚀 使用方法:")
    print("   方法1: python start_augment_monitor.py")
    print("   方法2: python augment_monitor_system.py")
    print("   方法3: python install.py  # 安装并自动启动")
    print()
    
    print("📋 系统工作流程:")
    print("   1️⃣ 系统启动并初始化数据库")
    print("   2️⃣ 自动发送JSON格式分析请求给Augment")
    print("   3️⃣ 监听Augment的JSON格式响应")
    print("   4️⃣ 验证响应是否为有效JSON格式")
    print("   5️⃣ 保存结构化数据到数据库和JSON文件")
    print()
    
    print("⚠️  重要提醒:")
    print("   • 确保Augment AI插件正常运行")
    print("   • 确保输入框位置正确 (1800, 900)")
    print("   • 系统会自动要求Augment输出JSON格式")
    print("   • 生成的JSON文件包含完整的分析数据")
    print()

def main():
    """
    主演示函数
    """
    show_json_analysis_info()
    show_json_requirements()
    
    # 尝试显示提示词预览
    if show_json_prompt_preview():
        print("✅ JSON提示词系统加载正常")
    else:
        print("⚠️ JSON提示词系统加载异常，请检查依赖")
    
    show_json_output_benefits()
    show_usage_instructions()
    
    print("=" * 70)
    print("🎉 JSON格式业务分析系统已准备就绪！")
    print("💡 特色功能：自动要求Augment输出结构化JSON数据")
    print("=" * 70)
    
    # 询问是否立即启动
    try:
        choice = input("\n是否立即启动JSON格式业务分析系统？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是', '启动']:
            print("\n🚀 正在启动JSON格式业务分析系统...")
            print("📋 系统将要求Augment输出结构化JSON数据...")
            from augment_monitor_system import main as monitor_main
            monitor_main()
        else:
            print("👋 演示结束，您可以稍后手动启动系统")
            print("💡 提醒：新系统会自动要求Augment输出JSON格式数据")
    except KeyboardInterrupt:
        print("\n👋 用户中断演示")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请手动运行: python start_augment_monitor.py")

if __name__ == "__main__":
    main()
