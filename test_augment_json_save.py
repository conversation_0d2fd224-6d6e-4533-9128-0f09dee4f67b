#!/usr/bin/env python3
"""
测试Augment自动保存JSON文件功能
"""

import os
import time
from datetime import datetime

def check_json_file_creation():
    """
    检查Augment是否创建了JSON文件
    """
    expected_file = "business_analysis_report.json"
    current_dir = os.getcwd()
    json_filepath = os.path.join(current_dir, expected_file)
    
    print("=" * 60)
    print("🔍 检查Augment创建的JSON文件")
    print("=" * 60)
    print(f"📁 当前目录: {current_dir}")
    print(f"📄 预期文件: {expected_file}")
    print(f"🎯 完整路径: {json_filepath}")
    print()
    
    if os.path.exists(json_filepath):
        print("✅ 找到Augment创建的JSON文件！")
        
        # 获取文件信息
        file_size = os.path.getsize(json_filepath)
        file_mtime = os.path.getmtime(json_filepath)
        file_time = datetime.fromtimestamp(file_mtime).strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"📊 文件大小: {file_size} 字节")
        print(f"⏰ 修改时间: {file_time}")
        
        # 尝试读取文件内容预览
        try:
            with open(json_filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
            print(f"📝 文件内容长度: {len(content)} 字符")
            
            # 显示前200字符作为预览
            preview = content[:200] + "..." if len(content) > 200 else content
            print("\n📋 文件内容预览:")
            print("-" * 40)
            print(preview)
            print("-" * 40)
            
            # 检查是否为有效JSON
            import json
            try:
                json_data = json.loads(content)
                print("✅ JSON格式验证通过")
                
                # 检查是否包含预期的结构
                if "project_analysis" in json_data:
                    print("✅ 包含预期的project_analysis结构")
                else:
                    print("⚠️ 未找到预期的project_analysis结构")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式验证失败: {e}")
                
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            
    else:
        print("❌ 未找到Augment创建的JSON文件")
        print("可能的原因:")
        print("  • Augment还未完成分析")
        print("  • Augment未使用save-file工具")
        print("  • 文件保存到了其他位置")
        
    print()

def show_directory_contents():
    """
    显示当前目录的文件列表
    """
    print("📁 当前目录文件列表:")
    print("-" * 40)
    
    current_dir = os.getcwd()
    files = []
    
    for item in os.listdir(current_dir):
        item_path = os.path.join(current_dir, item)
        if os.path.isfile(item_path):
            size = os.path.getsize(item_path)
            mtime = os.path.getmtime(item_path)
            time_str = datetime.fromtimestamp(mtime).strftime('%m-%d %H:%M')
            files.append((item, size, time_str))
    
    # 按修改时间排序
    files.sort(key=lambda x: x[2], reverse=True)
    
    for filename, size, time_str in files:
        if filename.endswith('.json'):
            print(f"📄 {filename} ({size} bytes, {time_str}) ⭐")
        else:
            print(f"📄 {filename} ({size} bytes, {time_str})")
    
    print("-" * 40)
    print()

def monitor_file_creation(timeout=60):
    """
    监控JSON文件的创建
    """
    expected_file = "business_analysis_report.json"
    current_dir = os.getcwd()
    json_filepath = os.path.join(current_dir, expected_file)
    
    print(f"⏳ 开始监控JSON文件创建 (超时: {timeout}秒)")
    print(f"🎯 监控文件: {json_filepath}")
    print()
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if os.path.exists(json_filepath):
            print("🎉 检测到JSON文件创建！")
            check_json_file_creation()
            return True
        
        # 每5秒检查一次
        time.sleep(5)
        elapsed = int(time.time() - start_time)
        print(f"⏳ 等待中... {elapsed}/{timeout}秒")
    
    print("⏰ 监控超时，未检测到JSON文件创建")
    return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("🧪 Augment JSON文件保存功能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示当前目录内容
    show_directory_contents()
    
    # 检查是否已存在JSON文件
    check_json_file_creation()
    
    print("=" * 60)
    print("📋 测试说明:")
    print("1. 运行augment_monitor_system.py启动分析")
    print("2. 系统会要求Augment使用save-file工具创建JSON文件")
    print("3. 本脚本可以检测和验证创建的JSON文件")
    print("4. 预期文件名: business_analysis_report.json")
    print("=" * 60)
    
    # 询问是否开始监控
    try:
        choice = input("\n是否开始监控JSON文件创建？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            monitor_file_creation()
        else:
            print("👋 测试结束")
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")

if __name__ == "__main__":
    main()
