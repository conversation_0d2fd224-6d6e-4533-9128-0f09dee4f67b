#!/usr/bin/env python3
"""
智能项目分析器 - 自动打开IDEA并分析指定项目
"""

import os
import sys
import time
import subprocess
import json
from datetime import datetime
from pathlib import Path

class SmartProjectAnalyzer:
    """
    智能项目分析器
    """
    
    def __init__(self):
        self.idea_paths = [
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2023.3\\bin\\idea64.exe",
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.1\\bin\\idea64.exe",
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2\\bin\\idea64.exe",
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.3\\bin\\idea64.exe",
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2023.3\\bin\\idea64.exe",
            "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.1\\bin\\idea64.exe",
            "C:\\Program Files (x86)\\JetBrains\\IntelliJ IDEA 2023.3\\bin\\idea64.exe",
            "C:\\Program Files (x86)\\JetBrains\\IntelliJ IDEA 2024.1\\bin\\idea64.exe",
            # 添加更多可能的路径
            "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\apps\\IDEA-U\\ch-0\\*\\bin\\idea64.exe"
        ]
        self.current_project_path = None
        
    def find_idea_executable(self):
        """
        查找IntelliJ IDEA可执行文件 - 增强版
        """
        print("🔍 正在查找IntelliJ IDEA...")

        # 首先检查预定义路径
        for path in self.idea_paths:
            if os.path.exists(path):
                print(f"✅ 找到IDEA: {path}")
                return path

        # 尝试通过注册表查找（Windows）
        try:
            import winreg
            print("🔍 尝试通过注册表查找IDEA...")

            # 查找JetBrains Toolbox安装的IDEA
            toolbox_paths = [
                "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\apps\\IDEA-U",
                "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\apps\\IDEA-C"
            ]

            for toolbox_path in toolbox_paths:
                expanded_path = os.path.expandvars(toolbox_path)
                if os.path.exists(expanded_path):
                    # 查找最新版本
                    for version_dir in os.listdir(expanded_path):
                        version_path = os.path.join(expanded_path, version_dir)
                        if os.path.isdir(version_path):
                            for build_dir in os.listdir(version_path):
                                idea_exe = os.path.join(version_path, build_dir, "bin", "idea64.exe")
                                if os.path.exists(idea_exe):
                                    print(f"✅ 通过Toolbox找到IDEA: {idea_exe}")
                                    return idea_exe
        except Exception as e:
            print(f"⚠️ 注册表查找失败: {e}")

        # 尝试使用where命令查找
        try:
            print("🔍 尝试使用where命令查找...")
            result = subprocess.run(['where', 'idea64'], capture_output=True, text=True, shell=True)
            if result.returncode == 0 and result.stdout.strip():
                idea_path = result.stdout.strip().split('\n')[0]
                print(f"✅ 通过where命令找到IDEA: {idea_path}")
                return idea_path
        except Exception as e:
            print(f"⚠️ where命令查找失败: {e}")

        print("❌ 未找到IntelliJ IDEA，将尝试手动启动")
        return None
    
    def validate_project_path(self, project_path):
        """
        验证项目路径是否有效
        """
        if not project_path:
            return False, "项目路径不能为空"
        
        path = Path(project_path)
        if not path.exists():
            return False, f"路径不存在: {project_path}"
        
        if not path.is_dir():
            return False, f"路径不是目录: {project_path}"
        
        # 检查是否包含常见的项目文件
        project_indicators = [
            "pom.xml", "build.gradle", "package.json", "requirements.txt",
            "setup.py", "Cargo.toml", "go.mod", ".git", ".idea", ".vscode"
        ]
        
        has_project_files = any((path / indicator).exists() for indicator in project_indicators)
        if not has_project_files:
            print(f"⚠️ 警告: {project_path} 可能不是一个项目目录")
        
        return True, "路径验证通过"
    
    def open_idea_with_project(self, project_path):
        """
        使用IntelliJ IDEA打开指定项目 - 增强版
        """
        idea_exe = self.find_idea_executable()

        print(f"🚀 正在打开新的IDEA实例并加载项目: {project_path}")

        try:
            if idea_exe:
                # 方法1：使用找到的IDEA可执行文件
                print(f"📍 使用IDEA路径: {idea_exe}")
                cmd = f'start "" "{idea_exe}" "{project_path}"'
                subprocess.run(cmd, shell=True, check=True)
                print("✅ IDEA启动命令已执行")
            else:
                # 方法2：尝试通过系统关联启动
                print("🔄 尝试通过系统关联启动IDEA...")

                # 检查项目类型并尝试不同的启动方式
                project_path_obj = Path(project_path)

                # 尝试直接打开项目目录
                if (project_path_obj / "pom.xml").exists():
                    # Maven项目
                    pom_file = project_path_obj / "pom.xml"
                    subprocess.run(f'start "" "{pom_file}"', shell=True)
                    print("✅ 尝试通过pom.xml启动")
                elif (project_path_obj / "build.gradle").exists():
                    # Gradle项目
                    gradle_file = project_path_obj / "build.gradle"
                    subprocess.run(f'start "" "{gradle_file}"', shell=True)
                    print("✅ 尝试通过build.gradle启动")
                elif (project_path_obj / ".idea").exists():
                    # IntelliJ项目
                    idea_dir = project_path_obj / ".idea"
                    subprocess.run(f'start "" "{idea_dir}"', shell=True)
                    print("✅ 尝试通过.idea目录启动")
                else:
                    # 通用方法：直接打开目录
                    subprocess.run(f'start "" "{project_path}"', shell=True)
                    print("✅ 尝试直接打开项目目录")

            print("⏳ 等待IDEA完全加载项目...")
            print("   请确保IDEA已经打开并加载了项目")
            time.sleep(15)  # 给更多时间让IDEA加载

            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ 启动IDEA失败: {e}")
            print("💡 建议：请手动打开IDEA并加载项目")
            return False
        except Exception as e:
            print(f"❌ 打开项目时出错: {e}")
            print("💡 建议：请手动打开IDEA并加载项目")
            return False
    
    def analyze_project_structure(self, project_path):
        """
        分析项目结构
        """
        print(f"📊 正在分析项目结构: {project_path}")
        
        analysis = {
            "project_path": str(project_path),
            "project_name": Path(project_path).name,
            "analysis_time": datetime.now().isoformat(),
            "file_count": 0,
            "directory_count": 0,
            "file_types": {},
            "project_type": "unknown",
            "main_files": [],
            "config_files": [],
            "documentation": []
        }
        
        try:
            path = Path(project_path)
            
            # 统计文件和目录
            for item in path.rglob("*"):
                if item.is_file():
                    analysis["file_count"] += 1
                    
                    # 统计文件类型
                    ext = item.suffix.lower()
                    if ext:
                        analysis["file_types"][ext] = analysis["file_types"].get(ext, 0) + 1
                    
                    # 识别重要文件
                    filename = item.name.lower()
                    if filename in ["main.py", "app.py", "index.js", "main.java", "main.cpp"]:
                        analysis["main_files"].append(str(item.relative_to(path)))
                    elif filename in ["config.json", "settings.py", "application.properties"]:
                        analysis["config_files"].append(str(item.relative_to(path)))
                    elif filename in ["readme.md", "readme.txt", "docs.md"]:
                        analysis["documentation"].append(str(item.relative_to(path)))
                        
                elif item.is_dir():
                    analysis["directory_count"] += 1
            
            # 识别项目类型
            if (path / "pom.xml").exists():
                analysis["project_type"] = "Maven Java Project"
            elif (path / "build.gradle").exists():
                analysis["project_type"] = "Gradle Project"
            elif (path / "package.json").exists():
                analysis["project_type"] = "Node.js Project"
            elif (path / "requirements.txt").exists() or (path / "setup.py").exists():
                analysis["project_type"] = "Python Project"
            elif (path / "Cargo.toml").exists():
                analysis["project_type"] = "Rust Project"
            elif (path / "go.mod").exists():
                analysis["project_type"] = "Go Project"
            
            print(f"✅ 项目分析完成:")
            print(f"   项目类型: {analysis['project_type']}")
            print(f"   文件数量: {analysis['file_count']}")
            print(f"   目录数量: {analysis['directory_count']}")
            print(f"   主要文件类型: {list(analysis['file_types'].keys())[:5]}")
            
            return analysis
            
        except Exception as e:
            print(f"❌ 项目分析失败: {e}")
            return None
    
    def generate_analysis_prompt(self, project_analysis):
        """
        生成针对特定项目的分析提示词
        """
        project_type = project_analysis.get("project_type", "unknown")
        project_name = project_analysis.get("project_name", "unknown")
        
        prompt = f"""请详细分析这个{project_type}项目 "{project_name}" 的业务流程和技术架构，并将分析结果以JSON格式保存到文件中。

**项目基本信息：**
- 项目名称: {project_name}
- 项目类型: {project_type}
- 文件数量: {project_analysis.get('file_count', 0)}
- 主要文件类型: {', '.join(list(project_analysis.get('file_types', {}).keys())[:5])}

**重要要求：**
1. 请将所有分析结果组织成结构化的JSON格式
2. **请使用save-file工具将JSON数据保存到文件 `{project_name}_analysis_report.json` 中**
3. 请特别关注这个{project_type}的特有架构和模式
4. JSON格式应包含以下字段结构：
   - project_analysis (项目分析主体)
     - project_overview (项目概述)
     - technical_architecture (技术架构)
     - business_process (业务流程)
     - feature_assessment (功能评估)
     - code_quality (代码质量)
     - improvement_suggestions (改进建议)
     - technical_debt_risks (技术债务风险)
   - analysis_metadata (分析元数据)

请基于项目的实际代码和结构，提供详细、专业的JSON格式分析报告。

**执行步骤：**
1. 使用codebase-retrieval工具深度分析项目代码
2. 分析项目的各个方面（架构、业务流程、代码质量等）
3. 将分析结果组织成完整的JSON格式
4. **使用save-file工具将完整的JSON数据保存到 `{project_name}_analysis_report.json` 文件中**

**重要提醒：请务必使用save-file工具创建JSON文件，不要只是显示JSON内容。**"""

        return prompt

    def auto_send_to_augment(self, prompt, project_name):
        """
        自动发送提示词给Augment - 改进版
        """
        print("⚠️ 重要提醒：请确保IDEA已经打开了正确的项目！")
        print(f"📁 目标项目: {self.current_project_path}")
        print(f"📊 预期报告: {project_name}_analysis_report.json")
        print()

        # 询问用户是否已经打开了正确的项目
        try:
            user_input = input("✋ 请确认IDEA是否已经打开了正确的项目？(y/n): ").strip().lower()
            if user_input not in ['y', 'yes', '是', '确认']:
                print("❌ 请先手动打开IDEA并加载正确的项目，然后重新运行分析器")
                print(f"💡 项目路径: {self.current_project_path}")
                return False
        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            return False

        try:
            # 导入Augment监听系统
            from augment_monitor_system import AugmentMonitorSystem

            print("🔧 正在初始化Augment监听系统...")

            # 创建监听系统实例
            monitor = AugmentMonitorSystem(input_position=(1800, 900))

            print("📤 正在发送分析请求给Augment...")
            print("⏳ 请等待Augment响应...")

            # 发送请求
            result = monitor.process_request(prompt)

            if result["success"]:
                print(f"✅ 请求发送成功！")
                print(f"   数据库ID: {result['database_id']}")
                print(f"   响应长度: {result['response_length']} 字符")
                print(f"   质量评分: {result['quality_score']}")

                # 检查是否生成了预期的JSON文件
                expected_file = f"{project_name}_analysis_report.json"
                if os.path.exists(expected_file):
                    print(f"🎉 分析报告已生成: {expected_file}")
                else:
                    print(f"⏳ 分析报告生成中，请稍候查看: {expected_file}")

                return True
            else:
                print(f"❌ 请求发送失败: {result.get('error', '未知错误')}")
                return False

        except ImportError:
            print("❌ 无法导入Augment监听系统，请确保augment_monitor_system.py存在")
            return False
        except KeyboardInterrupt:
            print("\n❌ 用户中断发送过程")
            return False
        except Exception as e:
            print(f"❌ 自动发送过程出错: {e}")
            return False

    def run_analysis(self, project_path):
        """
        运行完整的项目分析流程
        """
        print("=" * 70)
        print("🎯 智能项目分析器启动")
        print("=" * 70)
        
        # 1. 验证项目路径
        is_valid, message = self.validate_project_path(project_path)
        if not is_valid:
            print(f"❌ {message}")
            return False
        
        print(f"✅ {message}")
        self.current_project_path = project_path
        
        # 2. 分析项目结构
        project_analysis = self.analyze_project_structure(project_path)
        if not project_analysis:
            print("❌ 项目结构分析失败")
            return False
        
        # 3. 打开IDEA
        if not self.open_idea_with_project(project_path):
            print("❌ 无法打开IDEA，但可以继续分析")
        
        # 4. 生成分析提示词
        analysis_prompt = self.generate_analysis_prompt(project_analysis)
        
        # 5. 保存分析提示词到文件
        prompt_file = f"{project_analysis['project_name']}_analysis_prompt.txt"
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(analysis_prompt)
            print(f"✅ 分析提示词已保存到: {prompt_file}")
        except Exception as e:
            print(f"⚠️ 保存提示词失败: {e}")
        
        # 6. 显示分析提示词
        print("\n" + "=" * 70)
        print("📋 生成的分析提示词:")
        print("=" * 70)
        print(analysis_prompt)
        print("=" * 70)

        # 7. 自动发送给Augment
        print(f"\n🚀 正在自动发送提示词给Augment...")
        success = self.auto_send_to_augment(analysis_prompt, project_analysis['project_name'])

        if success:
            print(f"✅ 提示词已自动发送给Augment！")
            print(f"📊 Augment正在生成分析报告...")
            print(f"📁 分析结果将保存为: {project_analysis['project_name']}_analysis_report.json")
        else:
            print(f"⚠️ 自动发送失败，请手动复制提示词发送给Augment")

        print(f"\n🎉 项目分析流程完成！")
        print(f"📁 项目路径: {project_path}")
        print(f"💻 IDEA已打开项目")

        return True

def main():
    """
    主函数
    """
    analyzer = SmartProjectAnalyzer()
    
    print("🚀 智能项目分析器")
    print("=" * 50)
    
    # 获取用户输入的项目路径
    if len(sys.argv) > 1:
        project_path = sys.argv[1]
        print(f"📁 使用命令行参数指定的项目路径: {project_path}")
    else:
        project_path = input("请输入要分析的项目路径: ").strip().strip('"')
    
    if not project_path:
        print("❌ 项目路径不能为空")
        return
    
    # 运行分析
    success = analyzer.run_analysis(project_path)
    
    if success:
        print("\n✅ 分析完成！请在IDEA中使用Augment进行深度分析。")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()
