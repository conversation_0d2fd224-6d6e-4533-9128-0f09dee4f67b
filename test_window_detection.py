"""
测试窗口检测功能
"""

import pygetwindow as gw
import time

def test_window_detection():
    """
    测试窗口检测
    """
    print("=== 窗口检测测试 ===")
    
    try:
        # 获取所有窗口
        all_windows = gw.getAllWindows()
        print(f"检测到 {len(all_windows)} 个窗口")
        
        # 显示所有窗口信息
        print("\n所有窗口列表:")
        print("-" * 80)
        
        idea_candidates = []
        
        for i, window in enumerate(all_windows):
            if window.title and len(window.title.strip()) > 0:
                title = window.title.strip()
                size_info = f"{window.width}x{window.height}"
                
                print(f"{i+1:3d}. [{size_info:>10}] {title}")
                
                # 检查是否可能是IDEA窗口
                title_lower = title.lower()
                if any(keyword in title_lower for keyword in ['intellij', 'idea', 'jetbrains']):
                    idea_candidates.append((window, "关键词匹配"))
                elif any(ext in title_lower for ext in ['.java', '.py', '.js', '.kt', '.xml']):
                    idea_candidates.append((window, "文件扩展名匹配"))
                elif window.width > 1000 and window.height > 700:
                    if any(word in title_lower for word in ['project', 'src', 'main']):
                        idea_candidates.append((window, "大窗口+项目关键词"))
        
        # 显示IDEA候选窗口
        if idea_candidates:
            print(f"\n🎯 发现 {len(idea_candidates)} 个可能的IDEA窗口:")
            print("-" * 80)
            
            for i, (window, reason) in enumerate(idea_candidates):
                print(f"{i+1}. {window.title}")
                print(f"   原因: {reason}")
                print(f"   大小: {window.width}x{window.height}")
                print(f"   位置: ({window.left}, {window.top})")
                print()
                
            # 选择最可能的IDEA窗口
            best_candidate = None
            best_score = 0
            
            for window, reason in idea_candidates:
                score = 0
                title_lower = window.title.lower()
                
                # 评分系统
                if 'intellij' in title_lower:
                    score += 100
                elif 'idea' in title_lower:
                    score += 80
                elif 'jetbrains' in title_lower:
                    score += 60
                
                if any(ext in title_lower for ext in ['.java', '.py', '.js']):
                    score += 30
                
                if window.width > 1200 and window.height > 800:
                    score += 20
                
                if any(word in title_lower for word in ['project', 'ai-assisted', 'gate']):
                    score += 15
                
                print(f"窗口评分: {window.title[:50]}... = {score}")
                
                if score > best_score:
                    best_score = score
                    best_candidate = window
            
            if best_candidate:
                print(f"\n✅ 最佳候选窗口 (评分: {best_score}):")
                print(f"标题: {best_candidate.title}")
                print(f"大小: {best_candidate.width}x{best_candidate.height}")
                
                # 测试激活窗口
                try:
                    print("\n🎯 尝试激活窗口...")
                    best_candidate.activate()
                    time.sleep(2)
                    print("✅ 窗口激活成功")
                    return best_candidate
                except Exception as e:
                    print(f"❌ 窗口激活失败: {e}")
            else:
                print("❌ 未找到合适的IDEA窗口")
        else:
            print("❌ 未找到任何IDEA候选窗口")
            
        return None
        
    except Exception as e:
        print(f"❌ 窗口检测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    主函数
    """
    print("请确保IntelliJ IDEA已经打开，然后按Enter开始检测...")
    input()
    
    result = test_window_detection()
    
    if result:
        print(f"\n🎉 成功检测到IDEA窗口: {result.title}")
    else:
        print("\n❌ 未能检测到IDEA窗口")

if __name__ == "__main__":
    main()
