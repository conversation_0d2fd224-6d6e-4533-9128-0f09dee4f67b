"""
修复版Augment自动化测试
专门处理Augment面板打开问题
"""

import time
import pyautogui
import pyperclip
import subprocess
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fixed_augment_test(project_path=None):
    """
    修复版Augment测试，专门处理面板打开问题
    """
    print("=== 修复版Augment自动化测试 ===")
    
    try:
        # 1. 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 1
        print("✅ 初始化完成")
        
        # 2. 确认IDEA已打开
        print("🔍 确认IntelliJ IDEA状态...")
        input("请确保IntelliJ IDEA已打开并加载了项目，然后按Enter继续...")
        
        # 3. 专门处理Augment面板打开
        print("🤖 打开Augment面板...")
        
        success = open_augment_panel()
        
        if not success:
            print("❌ 无法自动打开Augment面板")
            print("请手动打开Augment面板:")
            print("方法1: 点击IDE右侧或左侧的'Augment'标签")
            print("方法2: 使用菜单 View -> Tool Windows -> Augment")
            print("方法3: 使用快捷键 Alt+A (如果有设置)")
            input("Augment面板打开后，按Enter继续...")
        
        # 4. 验证Augment面板是否打开
        print("🔍 验证Augment面板状态...")
        
        if verify_augment_panel():
            print("✅ Augment面板已打开")
        else:
            print("⚠️ 无法确认Augment面板状态")
            input("请确认Augment聊天界面可见，然后按Enter继续...")
        
        # 5. 发送测试消息
        print("📝 发送分析请求...")
        
        # 构造分析提示
        analysis_prompt = f"""请分析这个项目的代码结构，包括：

🏗️ **项目架构**
- 整体架构设计
- 模块组织结构
- 依赖关系分析

📊 **代码质量**
- 代码规范性检查
- 潜在问题识别
- 改进建议

🔧 **技术栈**
- 使用的技术和框架
- 第三方依赖分析
- 技术选型评估

请提供详细的分析报告。"""
        
        # 复制到剪贴板
        pyperclip.copy(analysis_prompt)
        print("✅ 分析提示已复制到剪贴板")
        
        # 尝试自动输入
        if try_send_message(analysis_prompt):
            print("✅ 消息发送成功")
        else:
            print("❌ 自动发送失败，请手动操作")
            print("请将剪贴板中的内容粘贴到Augment聊天框并发送")
            input("发送完成后按Enter继续...")
        
        # 6. 等待响应
        print("⏳ 等待Augment分析...")
        wait_for_analysis()
        
        # 7. 获取结果
        print("📋 获取分析结果...")
        result = get_analysis_result()
        
        if result:
            save_result(result)
            print("🎉 测试完成！")
        else:
            print("⚠️ 未能获取到分析结果")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def open_augment_panel():
    """
    尝试打开Augment面板的多种方法
    """
    print("🔧 尝试多种方法打开Augment面板...")
    
    methods = [
        ("使用Action搜索", try_action_search),
        ("点击侧边栏", try_sidebar_click),
        ("使用菜单", try_menu_access),
        ("尝试快捷键", try_shortcut_keys)
    ]
    
    for method_name, method_func in methods:
        try:
            print(f"🎯 尝试方法: {method_name}")
            if method_func():
                print(f"✅ {method_name} 成功")
                time.sleep(3)  # 等待面板打开
                return True
            else:
                print(f"❌ {method_name} 失败")
        except Exception as e:
            print(f"❌ {method_name} 异常: {e}")
    
    return False

def try_action_search():
    """
    使用Action搜索打开Augment
    """
    try:
        # 打开Action搜索
        pyautogui.hotkey('ctrl', 'shift', 'a')
        time.sleep(2)
        
        # 输入Augment
        pyautogui.write("Augment", interval=0.1)
        time.sleep(1)
        
        # 按Enter
        pyautogui.press('enter')
        time.sleep(2)
        
        return True
    except:
        return False

def try_sidebar_click():
    """
    尝试点击侧边栏的Augment标签
    """
    try:
        # 常见的侧边栏位置
        sidebar_positions = [
            # 右侧边栏
            (1420, 200), (1420, 300), (1420, 400), (1420, 500),
            # 左侧边栏
            (20, 200), (20, 300), (20, 400), (20, 500),
            # 底部
            (200, 850), (300, 850), (400, 850), (500, 850)
        ]
        
        for x, y in sidebar_positions:
            try:
                # 右键点击查看是否有Augment选项
                pyautogui.rightClick(x, y)
                time.sleep(1)
                
                # 查找Augment选项
                pyautogui.press('escape')  # 关闭右键菜单
                time.sleep(0.5)
                
                # 尝试直接点击
                pyautogui.click(x, y)
                time.sleep(1)
                
            except:
                continue
        
        return True
    except:
        return False

def try_menu_access():
    """
    通过菜单访问Augment
    """
    try:
        # 点击View菜单
        pyautogui.hotkey('alt', 'v')
        time.sleep(1)
        
        # 查找Tool Windows
        pyautogui.write("Tool Windows")
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(1)
        
        # 查找Augment
        pyautogui.write("Augment")
        time.sleep(1)
        pyautogui.press('enter')
        
        return True
    except:
        return False

def try_shortcut_keys():
    """
    尝试可能的快捷键
    """
    try:
        shortcuts = [
            ('alt', 'a'),
            ('ctrl', 'alt', 'a'),
            ('ctrl', 'shift', 'u'),  # 有些插件用这个
        ]
        
        for shortcut in shortcuts:
            pyautogui.hotkey(*shortcut)
            time.sleep(2)
        
        return True
    except:
        return False

def verify_augment_panel():
    """
    验证Augment面板是否打开
    """
    try:
        # 简单的验证：尝试在可能的输入区域输入测试文本
        test_positions = [
            (960, 800), (960, 850), (800, 800), (1120, 800),
            (960, 750), (960, 900), (700, 800), (1200, 800)
        ]
        
        for x, y in test_positions:
            try:
                pyautogui.click(x, y)
                time.sleep(0.5)
                
                # 尝试输入测试文本
                pyautogui.write("test")
                time.sleep(0.5)
                
                # 如果能输入，说明找到了输入框
                pyautogui.hotkey('ctrl', 'a')
                pyautogui.press('delete')
                
                print(f"✅ 在位置 ({x}, {y}) 找到可能的输入框")
                return True
                
            except:
                continue
        
        return False
    except:
        return False

def try_send_message(message):
    """
    尝试发送消息
    """
    try:
        # 尝试多个可能的输入位置
        input_positions = [
            (960, 800), (960, 850), (800, 800), (1120, 800),
            (960, 750), (960, 900), (700, 800), (1200, 800)
        ]
        
        for i, (x, y) in enumerate(input_positions):
            try:
                print(f"🎯 尝试输入位置 {i+1}: ({x}, {y})")
                
                # 点击位置
                pyautogui.click(x, y)
                time.sleep(1)
                
                # 清空可能的现有内容
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.5)
                pyautogui.press('delete')
                time.sleep(0.5)
                
                # 粘贴消息
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(2)
                
                # 发送消息
                pyautogui.press('enter')
                time.sleep(2)
                
                print(f"✅ 在位置 ({x}, {y}) 成功发送消息")
                return True
                
            except Exception as e:
                print(f"❌ 位置 {i+1} 失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        return False

def wait_for_analysis():
    """
    等待分析完成
    """
    print("⏳ 等待Augment分析完成...")
    print("观察Augment界面，等待分析结果出现")
    
    max_wait = 180  # 3分钟
    for i in range(0, max_wait, 15):
        print(f"⏰ 已等待 {i}/{max_wait} 秒")
        
        if i >= 60:  # 1分钟后开始询问
            response = input("分析完成了吗？(y/n/继续等待按Enter): ").strip().lower()
            if response == 'y':
                break
            elif response == 'n':
                continue
        else:
            time.sleep(15)

def get_analysis_result():
    """
    获取分析结果
    """
    try:
        print("📋 尝试获取分析结果...")
        
        # 尝试全选并复制
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)
        
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)
        
        result = pyperclip.paste()
        
        if result and len(result) > 50:
            print(f"✅ 获取到分析结果 ({len(result)} 字符)")
            return result
        else:
            print("❌ 自动获取失败")
            input("请手动复制Augment的分析结果，然后按Enter...")
            return pyperclip.paste()
            
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_result(result):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        filename = f"augment_fixed_analysis_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment项目分析报告 (修复版) ===\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")
            f.write(result)
        
        print(f"📁 结果已保存到: {filename}")
        
        # 显示预览
        print("\n📄 结果预览:")
        print("-" * 30)
        preview = result[:300] + "..." if len(result) > 300 else result
        print(preview)
        print("-" * 30)
        
        # 保存截图
        screenshot_file = f"augment_fixed_screenshot_{timestamp}.png"
        pyautogui.screenshot().save(screenshot_file)
        print(f"📸 截图已保存: {screenshot_file}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def main():
    """
    主函数
    """
    print("=== 修复版Augment自动化测试工具 ===")
    print("专门解决Augment面板打开问题")
    print()
    
    print("使用说明:")
    print("1. 确保IntelliJ IDEA已打开")
    print("2. 确保Augment插件已安装")
    print("3. 工具将自动尝试打开Augment面板")
    print("4. 如果自动打开失败，会提示手动操作")
    print()
    
    if input("准备开始测试吗？(y/n): ").strip().lower() != 'y':
        print("测试取消")
        return
    
    success = fixed_augment_test()
    
    if success:
        print("\n🎉 测试完成！")
        print("生成的文件:")
        print("- augment_fixed_analysis_*.txt (分析结果)")
        print("- augment_fixed_screenshot_*.png (截图)")
    else:
        print("\n❌ 测试未完成")

if __name__ == "__main__":
    main()
