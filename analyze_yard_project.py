#!/usr/bin/env python3
"""
专门分析yard-entrance-management项目的脚本
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def find_idea_executable():
    """
    查找IntelliJ IDEA可执行文件
    """
    idea_paths = [
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2023.3\\bin\\idea64.exe",
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.1\\bin\\idea64.exe",
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2\\bin\\idea64.exe",
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.3\\bin\\idea64.exe",
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2023.3\\bin\\idea64.exe",
        "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.1\\bin\\idea64.exe",
        "C:\\Program Files (x86)\\JetBrains\\IntelliJ IDEA 2023.3\\bin\\idea64.exe",
        "C:\\Program Files (x86)\\JetBrains\\IntelliJ IDEA 2024.1\\bin\\idea64.exe"
    ]
    
    print("🔍 正在查找IntelliJ IDEA...")
    
    for path in idea_paths:
        if os.path.exists(path):
            print(f"✅ 找到IDEA: {path}")
            return path
    
    # 尝试通过注册表或环境变量查找
    try:
        result = subprocess.run(['where', 'idea64'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            idea_path = result.stdout.strip().split('\n')[0]
            print(f"✅ 通过系统路径找到IDEA: {idea_path}")
            return idea_path
    except:
        pass
    
    print("❌ 未找到IntelliJ IDEA")
    return None

def open_idea_with_project(project_path):
    """
    使用IntelliJ IDEA打开指定项目
    """
    idea_exe = find_idea_executable()
    if not idea_exe:
        print("⚠️ 无法找到IDEA，将跳过IDEA启动步骤")
        return False
    
    print(f"🚀 正在打开IDEA并加载项目: {project_path}")
    
    try:
        # 使用subprocess启动IDEA
        cmd = f'"{idea_exe}" "{project_path}"'
        subprocess.Popen(cmd, shell=True)
        
        print("✅ IDEA启动命令已执行")
        print("⏳ 等待IDEA完全加载...")
        time.sleep(15)  # 等待IDEA加载
        
        return True
        
    except Exception as e:
        print(f"❌ 启动IDEA失败: {e}")
        return False

def analyze_project_structure(project_path):
    """
    分析项目结构
    """
    print(f"📊 正在分析项目结构: {project_path}")
    
    analysis = {
        "project_path": str(project_path),
        "project_name": Path(project_path).name,
        "analysis_time": time.strftime('%Y-%m-%d %H:%M:%S'),
        "file_count": 0,
        "directory_count": 0,
        "file_types": {},
        "project_type": "unknown",
        "main_files": [],
        "config_files": [],
        "documentation": [],
        "build_files": []
    }
    
    try:
        path = Path(project_path)
        
        # 统计文件和目录
        for item in path.rglob("*"):
            if item.is_file():
                analysis["file_count"] += 1
                
                # 统计文件类型
                ext = item.suffix.lower()
                if ext:
                    analysis["file_types"][ext] = analysis["file_types"].get(ext, 0) + 1
                
                # 识别重要文件
                filename = item.name.lower()
                if filename in ["main.py", "app.py", "index.js", "main.java", "application.java"]:
                    analysis["main_files"].append(str(item.relative_to(path)))
                elif filename in ["pom.xml", "build.gradle", "package.json", "requirements.txt"]:
                    analysis["build_files"].append(str(item.relative_to(path)))
                elif filename in ["config.json", "application.properties", "application.yml"]:
                    analysis["config_files"].append(str(item.relative_to(path)))
                elif filename in ["readme.md", "readme.txt", "docs.md"]:
                    analysis["documentation"].append(str(item.relative_to(path)))
                    
            elif item.is_dir():
                analysis["directory_count"] += 1
        
        # 识别项目类型
        if (path / "pom.xml").exists():
            analysis["project_type"] = "Maven Java Project"
        elif (path / "build.gradle").exists():
            analysis["project_type"] = "Gradle Project"
        elif (path / "package.json").exists():
            analysis["project_type"] = "Node.js Project"
        elif (path / "requirements.txt").exists() or (path / "setup.py").exists():
            analysis["project_type"] = "Python Project"
        elif (path / "Cargo.toml").exists():
            analysis["project_type"] = "Rust Project"
        elif (path / "go.mod").exists():
            analysis["project_type"] = "Go Project"
        elif any(f.endswith('.sln') for f in os.listdir(path) if os.path.isfile(path / f)):
            analysis["project_type"] = ".NET Solution"
        
        print(f"✅ 项目分析完成:")
        print(f"   项目类型: {analysis['project_type']}")
        print(f"   文件数量: {analysis['file_count']}")
        print(f"   目录数量: {analysis['directory_count']}")
        print(f"   主要文件类型: {list(analysis['file_types'].keys())[:10]}")
        
        return analysis
        
    except Exception as e:
        print(f"❌ 项目分析失败: {e}")
        return None

def generate_analysis_prompt(project_analysis):
    """
    生成针对yard-entrance-management项目的分析提示词
    """
    project_type = project_analysis.get("project_type", "unknown")
    project_name = project_analysis.get("project_name", "yard-entrance-management")
    
    prompt = f"""请详细分析这个{project_type}项目 "{project_name}" 的业务流程和技术架构，并将分析结果以JSON格式保存到文件中。

**项目基本信息：**
- 项目名称: {project_name}
- 项目类型: {project_type}
- 文件数量: {project_analysis.get('file_count', 0)}
- 主要文件类型: {', '.join(list(project_analysis.get('file_types', {}).keys())[:10])}
- 构建文件: {', '.join(project_analysis.get('build_files', [])[:5])}

**重要要求：**
1. 请将所有分析结果组织成结构化的JSON格式
2. **请使用save-file工具将JSON数据保存到文件 `{project_name}_analysis_report.json` 中**
3. 请特别关注这个{project_type}的特有架构和模式
4. 基于文件类型分析，这可能是一个园区入口管理系统，请重点分析：
   - 入口管理业务逻辑
   - 权限控制和安全机制
   - 数据存储和管理
   - 用户界面和交互设计
   - 系统集成和API设计

**JSON格式应包含以下字段结构：**
- project_analysis (项目分析主体)
  - project_overview (项目概述)
  - technical_architecture (技术架构)
  - business_process (业务流程)
  - feature_assessment (功能评估)
  - code_quality (代码质量)
  - improvement_suggestions (改进建议)
  - technical_debt_risks (技术债务风险)
- analysis_metadata (分析元数据)

请基于项目的实际代码和结构，提供详细、专业的JSON格式分析报告。

**执行步骤：**
1. 使用codebase-retrieval工具深度分析项目代码
2. 分析项目的各个方面（架构、业务流程、代码质量等）
3. 将分析结果组织成完整的JSON格式
4. **使用save-file工具将完整的JSON数据保存到 `{project_name}_analysis_report.json` 文件中**

**重要提醒：请务必使用save-file工具创建JSON文件，不要只是显示JSON内容。**"""

    return prompt

def main():
    """
    主函数
    """
    project_path = "D:\\project\\yard-entrance-management"
    
    print("=" * 70)
    print("🎯 yard-entrance-management 项目专用分析器")
    print("=" * 70)
    print(f"📁 目标项目: {project_path}")
    
    # 1. 验证项目路径
    if not os.path.exists(project_path):
        print(f"❌ 项目路径不存在: {project_path}")
        return False
    
    if not os.path.isdir(project_path):
        print(f"❌ 路径不是目录: {project_path}")
        return False
    
    print("✅ 项目路径验证通过")
    
    # 2. 分析项目结构
    project_analysis = analyze_project_structure(project_path)
    if not project_analysis:
        print("❌ 项目结构分析失败")
        return False
    
    # 3. 打开IDEA
    print("\n🚀 正在尝试打开IntelliJ IDEA...")
    idea_opened = open_idea_with_project(project_path)
    if idea_opened:
        print("✅ IDEA已成功打开项目")
    else:
        print("⚠️ IDEA打开失败，但可以继续分析")
    
    # 4. 生成分析提示词
    analysis_prompt = generate_analysis_prompt(project_analysis)
    
    # 5. 保存分析提示词到文件
    prompt_file = f"{project_analysis['project_name']}_analysis_prompt.txt"
    try:
        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(analysis_prompt)
        print(f"✅ 分析提示词已保存到: {prompt_file}")
    except Exception as e:
        print(f"⚠️ 保存提示词失败: {e}")
    
    # 6. 显示分析提示词
    print("\n" + "=" * 70)
    print("📋 生成的分析提示词:")
    print("=" * 70)
    print(analysis_prompt)
    print("=" * 70)
    
    # 7. 尝试自动发送给Augment
    print(f"\n🚀 正在尝试自动发送提示词给Augment...")
    try:
        from augment_monitor_system import AugmentMonitorSystem
        
        print("🔧 正在初始化Augment监听系统...")
        monitor = AugmentMonitorSystem(input_position=(1800, 900))
        
        print("📤 正在发送分析请求给Augment...")
        result = monitor.process_request(analysis_prompt)
        
        if result["success"]:
            print(f"✅ 请求发送成功！")
            print(f"   数据库ID: {result['database_id']}")
            print(f"   响应长度: {result['response_length']} 字符")
            print(f"   质量评分: {result['quality_score']}")
            
            expected_file = f"{project_analysis['project_name']}_analysis_report.json"
            print(f"📊 预期分析报告: {expected_file}")
        else:
            print(f"❌ 请求发送失败: {result.get('error', '未知错误')}")
            print("请手动复制上述提示词发送给Augment")
            
    except Exception as e:
        print(f"❌ 自动发送失败: {e}")
        print("请手动复制上述提示词发送给Augment")
    
    print(f"\n🎉 yard-entrance-management 项目分析准备完成！")
    print(f"📁 项目路径: {project_path}")
    print(f"💻 IDEA状态: {'已打开' if idea_opened else '未打开'}")
    print(f"📝 提示词文件: {prompt_file}")
    print(f"📊 预期报告: {project_analysis['project_name']}_analysis_report.json")
    
    return True

if __name__ == "__main__":
    main()
