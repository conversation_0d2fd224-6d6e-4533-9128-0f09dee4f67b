#!/usr/bin/env python3
"""
AI辅助项目自动业务分析系统启动脚本
"""

import sys
import os

def main():
    """
    启动AI辅助项目自动业务分析系统
    """
    print("=== AI辅助项目自动业务分析系统 ===")
    print("正在启动自动业务分析...")
    print("系统将自动分析项目架构、业务流程和技术特性")
    print("-" * 50)

    try:
        # 导入并运行业务分析系统
        from augment_monitor_system import main as monitor_main
        monitor_main()
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断业务分析")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 业务分析系统错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
