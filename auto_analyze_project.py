#!/usr/bin/env python3
"""
完全自动化项目分析器 - 一键分析任意项目
"""

import sys
import os
import time
from pathlib import Path

def main():
    """
    完全自动化的项目分析主函数
    """
    print("🚀 完全自动化项目分析器")
    print("=" * 60)
    print("功能：自动打开IDEA + 自动发送给Augment + 自动生成报告")
    print("=" * 60)
    
    # 获取项目路径
    if len(sys.argv) > 1:
        project_path = sys.argv[1]
        print(f"📁 使用命令行参数指定的项目路径: {project_path}")
    else:
        print("请输入要分析的项目路径：")
        print("示例：")
        print("  D:\\my-java-project")
        print("  C:\\Users\\<USER>\\Documents\\my-python-app")
        print("  /home/<USER>/my-node-project")
        print()
        project_path = input("项目路径: ").strip().strip('"')
    
    if not project_path:
        print("❌ 项目路径不能为空")
        return
    
    # 验证路径
    if not os.path.exists(project_path):
        print(f"❌ 路径不存在: {project_path}")
        return
    
    if not os.path.isdir(project_path):
        print(f"❌ 路径不是目录: {project_path}")
        return
    
    print(f"✅ 项目路径验证通过: {project_path}")
    print()
    
    try:
        # 导入智能分析器
        from smart_project_analyzer import SmartProjectAnalyzer
        
        print("🔧 正在启动智能项目分析器...")
        analyzer = SmartProjectAnalyzer()
        
        # 运行完全自动化分析
        print("🎯 开始完全自动化分析流程...")
        print("   1️⃣ 分析项目结构")
        print("   2️⃣ 识别项目类型")
        print("   3️⃣ 打开IntelliJ IDEA")
        print("   4️⃣ 生成分析提示词")
        print("   5️⃣ 自动发送给Augment")
        print("   6️⃣ 等待生成分析报告")
        print()
        
        success = analyzer.run_analysis(project_path)
        
        if success:
            project_name = Path(project_path).name
            expected_report = f"{project_name}_analysis_report.json"
            
            print("\n" + "=" * 60)
            print("🎉 自动化分析流程完成！")
            print("=" * 60)
            print(f"📁 项目路径: {project_path}")
            print(f"📊 预期报告: {expected_report}")
            print(f"💻 IDEA已打开项目")
            print(f"🤖 Augment已收到分析请求")
            print()
            
            # 等待并检查报告生成
            print("⏳ 正在等待Augment生成分析报告...")
            for i in range(30):  # 等待最多30秒
                if os.path.exists(expected_report):
                    print(f"🎊 分析报告已生成: {expected_report}")
                    
                    # 显示报告基本信息
                    try:
                        import json
                        with open(expected_report, 'r', encoding='utf-8') as f:
                            report_data = json.load(f)
                        
                        print("\n📋 报告内容概览:")
                        if 'project_analysis' in report_data:
                            analysis = report_data['project_analysis']
                            print(f"   ✓ 项目概述: {'✅' if 'project_overview' in analysis else '❌'}")
                            print(f"   ✓ 技术架构: {'✅' if 'technical_architecture' in analysis else '❌'}")
                            print(f"   ✓ 业务流程: {'✅' if 'business_process' in analysis else '❌'}")
                            print(f"   ✓ 功能评估: {'✅' if 'feature_assessment' in analysis else '❌'}")
                            print(f"   ✓ 代码质量: {'✅' if 'code_quality' in analysis else '❌'}")
                            print(f"   ✓ 改进建议: {'✅' if 'improvement_suggestions' in analysis else '❌'}")
                            print(f"   ✓ 技术债务: {'✅' if 'technical_debt_risks' in analysis else '❌'}")
                        
                        file_size = os.path.getsize(expected_report)
                        print(f"\n📊 报告统计:")
                        print(f"   文件大小: {file_size} 字节")
                        print(f"   生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                        
                    except Exception as e:
                        print(f"⚠️ 读取报告时出错: {e}")
                    
                    break
                
                print(f"   等待中... {i+1}/30 秒")
                time.sleep(1)
            else:
                print("⏰ 等待超时，请稍后手动检查报告文件")
            
            print("\n🎯 下一步操作建议:")
            print(f"   1. 在IDEA中查看项目代码")
            print(f"   2. 打开 {expected_report} 查看详细分析")
            print(f"   3. 根据改进建议优化项目")
            print(f"   4. 可以重新运行分析对比改进效果")
            
        else:
            print("\n❌ 自动化分析失败！")
            print("请检查：")
            print("   1. 项目路径是否正确")
            print("   2. 是否有足够的权限")
            print("   3. Augment是否正常运行")
            print("   4. 网络连接是否正常")
    
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保以下文件存在：")
        print("   - smart_project_analyzer.py")
        print("   - augment_monitor_system.py")
    except Exception as e:
        print(f"❌ 运行过程出错: {e}")
    
    print("\n👋 分析器已结束")

if __name__ == "__main__":
    main()
