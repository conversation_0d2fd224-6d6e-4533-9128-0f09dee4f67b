# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: client-workspaces.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'client-workspaces.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x63lient-workspaces.proto\x12,com.augmentcode.sidecar.rpc.clientInterfaces\"\x1d\n\x0eGetCWDResponse\x12\x0b\n\x03\x63wd\x18\x01 \x01(\t\"1\n\x18GetWorkspaceRootResponse\x12\x15\n\rworkspaceRoot\x18\x01 \x01(\t\"#\n\x0fReadFileRequest\x12\x10\n\x08\x66ilePath\x18\x01 \x01(\t\"w\n\x10ReadFileResponse\x12\x10\n\x08\x63ontents\x18\x01 \x01(\t\x12Q\n\x08\x66ilepath\x18\x02 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName\"6\n\x11QualifiedPathName\x12\x10\n\x08rootPath\x18\x01 \x01(\t\x12\x0f\n\x07relPath\x18\x02 \x01(\t\"w\n\x10WriteFileRequest\x12Q\n\x08\x66ilePath\x18\x01 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName\x12\x10\n\x08\x63ontents\x18\x02 \x01(\t\"f\n\x11\x44\x65leteFileRequest\x12Q\n\x08\x66ilePath\x18\x01 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName\"+\n\x1bGetQualifiedPathNameRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\"\x83\x01\n\x1cGetQualifiedPathNameResponse\x12V\n\x08\x66ilepath\x18\x01 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathNameH\x00\x88\x01\x01\x42\x0b\n\t_filepath\"P\n\x10\x46indFilesRequest\x12\x13\n\x0bincludeGlob\x18\x01 \x01(\t\x12\x13\n\x0b\x65xcludeGlob\x18\x02 \x01(\t\x12\x12\n\nmaxResults\x18\x03 \x01(\x05\"c\n\x11\x46indFilesResponse\x12N\n\x05\x66iles\x18\x01 \x03(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName\"\"\n\x12GetPathInfoRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\"\xee\x01\n\x13GetPathInfoResponse\x12I\n\x04type\x18\x01 \x01(\x0e\x32\x36.com.augmentcode.sidecar.rpc.clientInterfaces.FileTypeH\x00\x88\x01\x01\x12V\n\x08\x66ilepath\x18\x02 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathNameH\x01\x88\x01\x01\x12\x13\n\x06\x65xists\x18\x03 \x01(\x08H\x02\x88\x01\x01\x42\x07\n\x05_typeB\x0b\n\t_filepathB\t\n\x07_exists\"G\n\x14ListDirectoryRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\r\n\x05\x64\x65pth\x18\x02 \x01(\x05\x12\x12\n\nshowHidden\x18\x03 \x01(\x08\"T\n\x15ListDirectoryResponse\x12\x0f\n\x07\x65ntries\x18\x01 \x03(\t\x12\x19\n\x0c\x65rrorMessage\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\x0f\n\r_errorMessage\"\x80\x01\n\x04Rule\x12\x44\n\x04type\x18\x01 \x01(\x0e\x32\x36.com.augmentcode.sidecar.rpc.clientInterfaces.RuleType\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\"w\n\x10LoadRulesRequest\x12\x19\n\x11includeGuidelines\x18\x01 \x01(\x08\x12H\n\x0c\x63ontextRules\x18\x02 \x03(\x0b\x32\x32.com.augmentcode.sidecar.rpc.clientInterfaces.Rule\"V\n\x11LoadRulesResponse\x12\x41\n\x05rules\x18\x01 \x03(\x0b\x32\x32.com.augmentcode.sidecar.rpc.clientInterfaces.Rule*~\n\x08\x46ileType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04\x46ILE\x10\x01\x12\r\n\tDIRECTORY\x10\x02\x12\x11\n\rSYMBOLIC_LINK\x10@\x12\x19\n\x15SYMBOLIC_LINK_TO_FILE\x10\x41\x12\x1e\n\x1aSYMBOLIC_LINK_TO_DIRECTORY\x10\x42*@\n\x08RuleType\x12\x13\n\x0f\x41LWAYS_ATTACHED\x10\x00\x12\n\n\x06MANUAL\x10\x01\x12\x13\n\x0f\x41GENT_REQUESTED\x10\x02\x42J\n,com.augmentcode.sidecar.rpc.clientInterfacesB\x18\x43lientWorkspacesRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'client_workspaces_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n,com.augmentcode.sidecar.rpc.clientInterfacesB\030ClientWorkspacesRPCTypesP\001'
  _globals['_FILETYPE']._serialized_start=1732
  _globals['_FILETYPE']._serialized_end=1858
  _globals['_RULETYPE']._serialized_start=1860
  _globals['_RULETYPE']._serialized_end=1924
  _globals['_GETCWDRESPONSE']._serialized_start=73
  _globals['_GETCWDRESPONSE']._serialized_end=102
  _globals['_GETWORKSPACEROOTRESPONSE']._serialized_start=104
  _globals['_GETWORKSPACEROOTRESPONSE']._serialized_end=153
  _globals['_READFILEREQUEST']._serialized_start=155
  _globals['_READFILEREQUEST']._serialized_end=190
  _globals['_READFILERESPONSE']._serialized_start=192
  _globals['_READFILERESPONSE']._serialized_end=311
  _globals['_QUALIFIEDPATHNAME']._serialized_start=313
  _globals['_QUALIFIEDPATHNAME']._serialized_end=367
  _globals['_WRITEFILEREQUEST']._serialized_start=369
  _globals['_WRITEFILEREQUEST']._serialized_end=488
  _globals['_DELETEFILEREQUEST']._serialized_start=490
  _globals['_DELETEFILEREQUEST']._serialized_end=592
  _globals['_GETQUALIFIEDPATHNAMEREQUEST']._serialized_start=594
  _globals['_GETQUALIFIEDPATHNAMEREQUEST']._serialized_end=637
  _globals['_GETQUALIFIEDPATHNAMERESPONSE']._serialized_start=640
  _globals['_GETQUALIFIEDPATHNAMERESPONSE']._serialized_end=771
  _globals['_FINDFILESREQUEST']._serialized_start=773
  _globals['_FINDFILESREQUEST']._serialized_end=853
  _globals['_FINDFILESRESPONSE']._serialized_start=855
  _globals['_FINDFILESRESPONSE']._serialized_end=954
  _globals['_GETPATHINFOREQUEST']._serialized_start=956
  _globals['_GETPATHINFOREQUEST']._serialized_end=990
  _globals['_GETPATHINFORESPONSE']._serialized_start=993
  _globals['_GETPATHINFORESPONSE']._serialized_end=1231
  _globals['_LISTDIRECTORYREQUEST']._serialized_start=1233
  _globals['_LISTDIRECTORYREQUEST']._serialized_end=1304
  _globals['_LISTDIRECTORYRESPONSE']._serialized_start=1306
  _globals['_LISTDIRECTORYRESPONSE']._serialized_end=1390
  _globals['_RULE']._serialized_start=1393
  _globals['_RULE']._serialized_end=1521
  _globals['_LOADRULESREQUEST']._serialized_start=1523
  _globals['_LOADRULESREQUEST']._serialized_end=1642
  _globals['_LOADRULESRESPONSE']._serialized_start=1644
  _globals['_LOADRULESRESPONSE']._serialized_end=1730
# @@protoc_insertion_point(module_scope)
