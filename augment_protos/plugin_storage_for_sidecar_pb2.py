# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: plugin-storage-for-sidecar.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'plugin-storage-for-sidecar.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n plugin-storage-for-sidecar.proto\x12,com.augmentcode.sidecar.rpc.clientInterfaces\"r\n\x14GetStateValueRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12M\n\x05scope\x18\x02 \x01(\x0e\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope\"=\n\x15GetStateValueResponse\x12\x16\n\tjsonValue\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\x0c\n\n_jsonValue\"\x98\x01\n\x14SetStateValueRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x16\n\tjsonValue\x18\x02 \x01(\tH\x00\x88\x01\x01\x12M\n\x05scope\x18\x03 \x01(\x0e\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScopeB\x0c\n\n_jsonValue*-\n\x10PluginStateScope\x12\n\n\x06GLOBAL\x10\x00\x12\r\n\tWORKSPACE\x10\x01\x42Q\n,com.augmentcode.sidecar.rpc.clientInterfacesB\x1fPluginStorageForSidecarRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'plugin_storage_for_sidecar_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n,com.augmentcode.sidecar.rpc.clientInterfacesB\037PluginStorageForSidecarRPCTypesP\001'
  _globals['_PLUGINSTATESCOPE']._serialized_start=416
  _globals['_PLUGINSTATESCOPE']._serialized_end=461
  _globals['_GETSTATEVALUEREQUEST']._serialized_start=82
  _globals['_GETSTATEVALUEREQUEST']._serialized_end=196
  _globals['_GETSTATEVALUERESPONSE']._serialized_start=198
  _globals['_GETSTATEVALUERESPONSE']._serialized_end=259
  _globals['_SETSTATEVALUEREQUEST']._serialized_start=262
  _globals['_SETSTATEVALUEREQUEST']._serialized_end=414
# @@protoc_insertion_point(module_scope)
