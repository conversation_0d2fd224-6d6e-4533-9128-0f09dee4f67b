# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import augment_chat_simple_pb2 as augment__chat__simple__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in augment_chat_simple_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WebviewChatServiceStub(object):
    """简化版的Augment聊天服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ChatUserMessage = channel.unary_stream(
                '/com.augmentcode.rpc.WebviewChatService/ChatUserMessage',
                request_serializer=augment__chat__simple__pb2.ChatUserMessageRequest.SerializeToString,
                response_deserializer=augment__chat__simple__pb2.ChatModelReply.FromString,
                _registered_method=True)
        self.ChatUserCancel = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatUserCancel',
                request_serializer=augment__chat__simple__pb2.ChatUserCancelRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class WebviewChatServiceServicer(object):
    """简化版的Augment聊天服务
    """

    def ChatUserMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatUserCancel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WebviewChatServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ChatUserMessage': grpc.unary_stream_rpc_method_handler(
                    servicer.ChatUserMessage,
                    request_deserializer=augment__chat__simple__pb2.ChatUserMessageRequest.FromString,
                    response_serializer=augment__chat__simple__pb2.ChatModelReply.SerializeToString,
            ),
            'ChatUserCancel': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatUserCancel,
                    request_deserializer=augment__chat__simple__pb2.ChatUserCancelRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.augmentcode.rpc.WebviewChatService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.augmentcode.rpc.WebviewChatService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WebviewChatService(object):
    """简化版的Augment聊天服务
    """

    @staticmethod
    def ChatUserMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatUserMessage',
            augment__chat__simple__pb2.ChatUserMessageRequest.SerializeToString,
            augment__chat__simple__pb2.ChatModelReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatUserCancel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatUserCancel',
            augment__chat__simple__pb2.ChatUserCancelRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
