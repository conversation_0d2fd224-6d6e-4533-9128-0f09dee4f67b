# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: sidecarrpc.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'sidecarrpc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import tools_pb2 as tools__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10sidecarrpc.proto\x12\x1b\x63om.augmentcode.sidecar.rpc\x1a\x0btools.proto\x1a\x19google/protobuf/any.proto\x1a google/protobuf/descriptor.proto\"c\n\x0eJSONRPCRequest\x12\x0f\n\x07jsonrpc\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x05\x12\x0e\n\x06method\x18\x03 \x01(\t\x12$\n\x06params\x18\x04 \x01(\x0b\x32\x14.google.protobuf.Any\"\\\n\x13JSONRPCNotification\x12\x0f\n\x07jsonrpc\x18\x01 \x01(\t\x12\x0e\n\x06method\x18\x02 \x01(\t\x12$\n\x06params\x18\x03 \x01(\x0b\x32\x14.google.protobuf.Any\"\x92\x01\n\x0fJSONRPCResponse\x12\x0f\n\x07jsonrpc\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x05\x12$\n\x06result\x18\x03 \x01(\x0b\x32\x14.google.protobuf.Any\x12<\n\x05\x65rror\x18\x04 \x01(\x0b\x32-.com.augmentcode.sidecar.rpc.JSONRPCErrorData\"1\n\x10JSONRPCErrorData\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\"g\n\x10InitializeParams\x12\x12\n\nprocess_id\x18\x01 \x01(\x03\x12?\n\x0c\x63\x61pabilities\x18\x02 \x01(\x0b\x32).com.augmentcode.sidecar.rpc.Capabilities\"\x97\x01\n\x0c\x43\x61pabilities\x12?\n\x0c\x66\x65\x61tureFlags\x18\x01 \x01(\x0b\x32).com.augmentcode.sidecar.rpc.SidecarFlags\x12\x46\n\x0cinitialState\x18\x02 \x01(\x0b\x32\x30.com.augmentcode.sidecar.rpc.SidecarInitialState\"\x97\t\n\x0cSidecarFlags\x12\x1b\n\x13\x65nableChatWithTools\x18\x01 \x01(\x08\x12\x17\n\x0f\x65nableAgentMode\x18\x02 \x01(\x08\x12\x15\n\ragentEditTool\x18\x03 \x01(\t\x12\x1c\n\x14memories_params_json\x18\x04 \x01(\t\x12 \n\x18\x61gentEditToolMinViewSize\x18\x05 \x01(\x05\x12\x1f\n\x17\x61gentEditToolSchemaType\x18\x06 \x01(\t\x12-\n%agentEditToolFuzzyMatchSuccessMessage\x18\x07 \x01(\t\x12&\n\x1e\x61gentEditToolFuzzyMatchMaxDiff\x18\x08 \x01(\x05\x12+\n#agentEditToolFuzzyMatchMaxDiffRatio\x18\t \x01(\x01\x12<\n4agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs\x18\n \x01(\x05\x12(\n agentEditToolEnableFuzzyMatching\x18\x0b \x01(\x08\x12)\n!agentEditToolInstructionsReminder\x18\x0c \x01(\x08\x12&\n\x1e\x61gentEditToolShowResultSnippet\x18\r \x01(\x08\x12-\n%agentSaveFileToolInstructionsReminder\x18\x0e \x01(\x08\x12\x16\n\x0e\x65nableTaskList\x18\x0f \x01(\x08\x12\x1d\n\x15\x61gentEditToolMaxLines\x18\x10 \x01(\x05\x12\x1c\n\x14grepSearchToolEnable\x18\x11 \x01(\x08\x12\"\n\x1agrepSearchToolTimelimitSec\x18\x12 \x01(\x05\x12&\n\x1egrepSearchToolOutputCharsLimit\x18\x13 \x01(\x05\x12%\n\x1dgrepSearchToolNumContextLines\x18\x14 \x01(\x05\x12!\n\x19\x65nableSupportToolUseStart\x18\x15 \x01(\x08\x12\x19\n\x11useHistorySummary\x18\x16 \x01(\x08\x12\x1e\n\x16historySummaryMaxChars\x18\x17 \x01(\x05\x12 \n\x18historySummaryLowerChars\x18\x18 \x01(\x05\x12\x1c\n\x14\x65nableNewThreadsList\x18\x19 \x01(\x08\x12\x1c\n\x14historySummaryPrompt\x18\x1a \x01(\t\x12\'\n\x1f\x65nableUntruncatedContentStorage\x18\x1b \x01(\x08\x12%\n\x1dmaxLinesTerminalProcessOutput\x18\x1c \x01(\x05\x12\x1c\n\x14\x65nableCommitIndexing\x18\x1d \x01(\x08\x12\x19\n\x11maxCommitsToIndex\x18\x1e \x01(\x05\x12\x1d\n\x15\x65nableExchangeStorage\x18\x1f \x01(\x08\x12!\n\x19\x65nableToolUseStateStorage\x18  \x01(\x08\x12\x1c\n\x14\x65nableAgentSwarmMode\x18! \x01(\x08\"o\n\x13SidecarInitialState\x12=\n\x08\x63hatMode\x18\x01 \x01(\x0e\x32+.com.augmentcode.sidecar.rpc.tools.ChatMode\x12\x19\n\x11memories_abs_path\x18\x03 \x01(\t\"Y\n\x10InitializeResult\x12\x45\n\x0c\x63\x61pabilities\x18\x01 \x01(\x0b\x32/.com.augmentcode.sidecar.rpc.ServerCapabilities\"a\n\x12ServerCapabilities\x12K\n\x10textDocumentSync\x18\x01 \x01(\x0e\x32\x31.com.augmentcode.sidecar.rpc.TextDocumentSyncKind\"O\n\x13ReadFileInputSchema\x12\x38\n\tfile_path\x18\x01 \x01(\tB%\xca\xd9t\x1dThe path of the file to read.\xd0\xd9t\x01\"\xf8\x01\n\x13SaveFileInputSchema\x12\x38\n\tfile_path\x18\x01 \x01(\tB%\xca\xd9t\x1dThe path of the file to save.\xd0\xd9t\x01\x12>\n\x0c\x66ile_content\x18\x02 \x01(\tB(\xca\xd9t The content of the file to save.\xd0\xd9t\x01\x12g\n\x15\x61\x64\x64_last_line_newline\x18\x03 \x01(\x08\x42H\xca\xd9t@Whether to add a newline at the end of the file (default: true).\xd0\xd9t\x00\"o\n\x1c\x43odebaseRetrievalInputSchema\x12O\n\x13information_request\x18\x01 \x01(\tB2\xca\xd9t*A description of the information you need.\xd0\xd9t\x01\"/\n\x1cProcessWebviewMessageRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\"0\n\x1dProcessWebviewMessageResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\"\xb3\x03\n\x18LaunchProcessInputSchema\x12\x35\n\x07\x63ommand\x18\x01 \x01(\tB$\xca\xd9t\x1cThe shell command to execute\xd0\xd9t\x01\x12\x42\n\x04wait\x18\x02 \x01(\x08\x42\x34\xca\xd9t,Whether to wait for the command to complete.\xd0\xd9t\x01\x12\xb9\x01\n\x10max_wait_seconds\x18\x03 \x01(\x03\x42\x9e\x01\xca\xd9t\x95\x01Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.\xd0\xd9t\x01\x12`\n\x03\x63wd\x18\x04 \x01(\tBS\xca\xd9tKRequired parameter. Absolute path to the working directory for the command.\xd0\xd9t\x01\"K\n\x16KillProcessInputSchema\x12\x31\n\x0bterminal_id\x18\x01 \x01(\x03\x42\x1c\xca\xd9t\x14Terminal ID to kill.\xd0\xd9t\x01\"\xcf\x02\n\x16ReadProcessInputSchema\x12\x36\n\x0bterminal_id\x18\x01 \x01(\x03\x42!\xca\xd9t\x19Terminal ID to read from.\xd0\xd9t\x01\x12\x42\n\x04wait\x18\x02 \x01(\x08\x42\x34\xca\xd9t,Whether to wait for the command to complete.\xd0\xd9t\x01\x12\xb8\x01\n\x10max_wait_seconds\x18\x03 \x01(\x03\x42\x9d\x01\xca\xd9t\x94\x01Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minutes may be a good default: increase from there if needed.\xd0\xd9t\x01\"\x93\x01\n\x17WriteProcessInputSchema\x12\x35\n\x0bterminal_id\x18\x01 \x01(\x03\x42 \xca\xd9t\x18Terminal ID to write to.\xd0\xd9t\x01\x12\x41\n\ninput_text\x18\x02 \x01(\tB-\xca\xd9t%Text to write to the process\'s stdin.\xd0\xd9t\x01\"\x1a\n\x18ListProcessesInputSchema\"\xa5\x01\n\x16WaitProcessInputSchema\x12\x35\n\x0bterminal_id\x18\x01 \x01(\x03\x42 \xca\xd9t\x18Terminal ID to wait for.\xd0\xd9t\x01\x12T\n\x0cwait_seconds\x18\x02 \x01(\x03\x42>\xca\xd9t6Number of seconds to wait for the process to complete.\xd0\xd9t\x01\"D\n\x0eProgressParams\x12\r\n\x05token\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.google.protobuf.Any*;\n\x14TextDocumentSyncKind\x12\x08\n\x04None\x10\x00\x12\x08\n\x04\x46ull\x10\x01\x12\x0f\n\x0bIncremental\x10\x02:;\n\x12schema_description\x12\x1d.google.protobuf.FieldOptions\x18\x99\xcb\x0e \x01(\t:8\n\x0fschema_required\x12\x1d.google.protobuf.FieldOptions\x18\x9a\xcb\x0e \x01(\x08\x42\x30\n\x1b\x63om.augmentcode.sidecar.rpcB\x0fSidecarRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'sidecarrpc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033com.augmentcode.sidecar.rpcB\017SidecarRPCTypesP\001'
  _globals['_READFILEINPUTSCHEMA'].fields_by_name['file_path']._loaded_options = None
  _globals['_READFILEINPUTSCHEMA'].fields_by_name['file_path']._serialized_options = b'\312\331t\035The path of the file to read.\320\331t\001'
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['file_path']._loaded_options = None
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['file_path']._serialized_options = b'\312\331t\035The path of the file to save.\320\331t\001'
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['file_content']._loaded_options = None
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['file_content']._serialized_options = b'\312\331t The content of the file to save.\320\331t\001'
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['add_last_line_newline']._loaded_options = None
  _globals['_SAVEFILEINPUTSCHEMA'].fields_by_name['add_last_line_newline']._serialized_options = b'\312\331t@Whether to add a newline at the end of the file (default: true).\320\331t\000'
  _globals['_CODEBASERETRIEVALINPUTSCHEMA'].fields_by_name['information_request']._loaded_options = None
  _globals['_CODEBASERETRIEVALINPUTSCHEMA'].fields_by_name['information_request']._serialized_options = b'\312\331t*A description of the information you need.\320\331t\001'
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['command']._loaded_options = None
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['command']._serialized_options = b'\312\331t\034The shell command to execute\320\331t\001'
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['wait']._loaded_options = None
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['wait']._serialized_options = b'\312\331t,Whether to wait for the command to complete.\320\331t\001'
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['max_wait_seconds']._loaded_options = None
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['max_wait_seconds']._serialized_options = b'\312\331t\225\001Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.\320\331t\001'
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['cwd']._loaded_options = None
  _globals['_LAUNCHPROCESSINPUTSCHEMA'].fields_by_name['cwd']._serialized_options = b'\312\331tKRequired parameter. Absolute path to the working directory for the command.\320\331t\001'
  _globals['_KILLPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._loaded_options = None
  _globals['_KILLPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._serialized_options = b'\312\331t\024Terminal ID to kill.\320\331t\001'
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._loaded_options = None
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._serialized_options = b'\312\331t\031Terminal ID to read from.\320\331t\001'
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['wait']._loaded_options = None
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['wait']._serialized_options = b'\312\331t,Whether to wait for the command to complete.\320\331t\001'
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['max_wait_seconds']._loaded_options = None
  _globals['_READPROCESSINPUTSCHEMA'].fields_by_name['max_wait_seconds']._serialized_options = b'\312\331t\224\001Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minutes may be a good default: increase from there if needed.\320\331t\001'
  _globals['_WRITEPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._loaded_options = None
  _globals['_WRITEPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._serialized_options = b'\312\331t\030Terminal ID to write to.\320\331t\001'
  _globals['_WRITEPROCESSINPUTSCHEMA'].fields_by_name['input_text']._loaded_options = None
  _globals['_WRITEPROCESSINPUTSCHEMA'].fields_by_name['input_text']._serialized_options = b'\312\331t%Text to write to the process\'s stdin.\320\331t\001'
  _globals['_WAITPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._loaded_options = None
  _globals['_WAITPROCESSINPUTSCHEMA'].fields_by_name['terminal_id']._serialized_options = b'\312\331t\030Terminal ID to wait for.\320\331t\001'
  _globals['_WAITPROCESSINPUTSCHEMA'].fields_by_name['wait_seconds']._loaded_options = None
  _globals['_WAITPROCESSINPUTSCHEMA'].fields_by_name['wait_seconds']._serialized_options = b'\312\331t6Number of seconds to wait for the process to complete.\320\331t\001'
  _globals['_TEXTDOCUMENTSYNCKIND']._serialized_start=4071
  _globals['_TEXTDOCUMENTSYNCKIND']._serialized_end=4130
  _globals['_JSONRPCREQUEST']._serialized_start=123
  _globals['_JSONRPCREQUEST']._serialized_end=222
  _globals['_JSONRPCNOTIFICATION']._serialized_start=224
  _globals['_JSONRPCNOTIFICATION']._serialized_end=316
  _globals['_JSONRPCRESPONSE']._serialized_start=319
  _globals['_JSONRPCRESPONSE']._serialized_end=465
  _globals['_JSONRPCERRORDATA']._serialized_start=467
  _globals['_JSONRPCERRORDATA']._serialized_end=516
  _globals['_INITIALIZEPARAMS']._serialized_start=518
  _globals['_INITIALIZEPARAMS']._serialized_end=621
  _globals['_CAPABILITIES']._serialized_start=624
  _globals['_CAPABILITIES']._serialized_end=775
  _globals['_SIDECARFLAGS']._serialized_start=778
  _globals['_SIDECARFLAGS']._serialized_end=1953
  _globals['_SIDECARINITIALSTATE']._serialized_start=1955
  _globals['_SIDECARINITIALSTATE']._serialized_end=2066
  _globals['_INITIALIZERESULT']._serialized_start=2068
  _globals['_INITIALIZERESULT']._serialized_end=2157
  _globals['_SERVERCAPABILITIES']._serialized_start=2159
  _globals['_SERVERCAPABILITIES']._serialized_end=2256
  _globals['_READFILEINPUTSCHEMA']._serialized_start=2258
  _globals['_READFILEINPUTSCHEMA']._serialized_end=2337
  _globals['_SAVEFILEINPUTSCHEMA']._serialized_start=2340
  _globals['_SAVEFILEINPUTSCHEMA']._serialized_end=2588
  _globals['_CODEBASERETRIEVALINPUTSCHEMA']._serialized_start=2590
  _globals['_CODEBASERETRIEVALINPUTSCHEMA']._serialized_end=2701
  _globals['_PROCESSWEBVIEWMESSAGEREQUEST']._serialized_start=2703
  _globals['_PROCESSWEBVIEWMESSAGEREQUEST']._serialized_end=2750
  _globals['_PROCESSWEBVIEWMESSAGERESPONSE']._serialized_start=2752
  _globals['_PROCESSWEBVIEWMESSAGERESPONSE']._serialized_end=2800
  _globals['_LAUNCHPROCESSINPUTSCHEMA']._serialized_start=2803
  _globals['_LAUNCHPROCESSINPUTSCHEMA']._serialized_end=3238
  _globals['_KILLPROCESSINPUTSCHEMA']._serialized_start=3240
  _globals['_KILLPROCESSINPUTSCHEMA']._serialized_end=3315
  _globals['_READPROCESSINPUTSCHEMA']._serialized_start=3318
  _globals['_READPROCESSINPUTSCHEMA']._serialized_end=3653
  _globals['_WRITEPROCESSINPUTSCHEMA']._serialized_start=3656
  _globals['_WRITEPROCESSINPUTSCHEMA']._serialized_end=3803
  _globals['_LISTPROCESSESINPUTSCHEMA']._serialized_start=3805
  _globals['_LISTPROCESSESINPUTSCHEMA']._serialized_end=3831
  _globals['_WAITPROCESSINPUTSCHEMA']._serialized_start=3834
  _globals['_WAITPROCESSINPUTSCHEMA']._serialized_end=3999
  _globals['_PROGRESSPARAMS']._serialized_start=4001
  _globals['_PROGRESSPARAMS']._serialized_end=4069
# @@protoc_insertion_point(module_scope)
