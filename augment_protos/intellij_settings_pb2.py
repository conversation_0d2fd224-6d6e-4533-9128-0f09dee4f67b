# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: intellij-settings.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'intellij-settings.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
import augment_pb2 as augment__pb2
import tools_pb2 as tools__pb2
import intellij_chat_pb2 as intellij__chat__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17intellij-settings.proto\x12\x13\x63om.augmentcode.rpc\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/descriptor.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\raugment.proto\x1a\x0btools.proto\x1a\x13intellij-chat.proto\"0\n\x13SettingsPanelLoaded:\x19\xba\xe8t\x15settings-panel-loaded\"1\n\x17ToolConfigLoadedRequest:\x16\xba\xe8t\x12tool-config-loaded\"}\n\x1cToolConfigInitializeResponse\x12\x41\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x33.com.augmentcode.rpc.SettingsInitializeResponseData:\x1a\xba\xe8t\x16tool-config-initialize\"\xc9\x01\n\x1aSettingsComponentSupported\x12\x18\n\x10workspaceContext\x18\x01 \x01(\x08\x12\x15\n\rmcpServerList\x18\x02 \x01(\x08\x12\x13\n\x0borientation\x18\x03 \x01(\x08\x12\x13\n\x0bremoteTools\x18\x04 \x01(\x08\x12\x16\n\x0euserGuidelines\x18\x05 \x01(\x08\x12\x17\n\x0fmcpServerImport\x18\x06 \x01(\x08\x12\x10\n\x08terminal\x18\x07 \x01(\x08\x12\r\n\x05rules\x18\x08 \x01(\x08\"\x92\x03\n\x1eSettingsInitializeResponseData\x12\x36\n\x0btoolConfigs\x18\x01 \x03(\x0b\x32!.com.augmentcode.rpc.ToolSettings\x12P\n\thostTools\x18\x02 \x03(\x0b\x32=.com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings\x12\x1b\n\x13\x65nableDebugFeatures\x18\x03 \x01(\x08\x12\x17\n\x0f\x65nableAgentMode\x18\x04 \x01(\x08\x12S\n\x1asettingsComponentSupported\x18\x05 \x01(\x0b\x32/.com.augmentcode.rpc.SettingsComponentSupported\x12\x39\n\nguidelines\x18\x06 \x01(\x0b\x32%.com.augmentcode.rpc.GuidelinesStates\x12 \n\x18\x65nableInitialOrientation\x18\x07 \x01(\x08\"B\n\x0cToolSettings\x12\x0e\n\x06\x63onfig\x18\x01 \x01(\t\x12\x14\n\x0cisConfigured\x18\x02 \x01(\x08\x12\x0c\n\x04name\x18\x03 \x01(\t\"\x8a\x01\n\x1fToolConfigGetDefinitionsRequest\x12\x46\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x38.com.augmentcode.rpc.ToolConfigGetDefinitionsRequestData:\x1f\xba\xe8t\x1btool-config-get-definitions\"7\n#ToolConfigGetDefinitionsRequestData\x12\x10\n\x08useCache\x18\x01 \x01(\x08\"\x8b\x01\n\x1dToolConfigDefinitionsResponse\x12\x44\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x36.com.augmentcode.rpc.ToolConfigDefinitionsResponseData:$\xba\xe8t tool-config-definitions-response\"u\n!ToolConfigDefinitionsResponseData\x12P\n\thostTools\x18\x01 \x03(\x0b\x32=.com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings\"k\n\x15ToolConfigSaveRequest\x12<\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32..com.augmentcode.rpc.ToolConfigSaveRequestData:\x14\xba\xe8t\x10tool-config-save\"W\n\x19ToolConfigSaveRequestData\x12\x12\n\ntoolConfig\x18\x01 \x01(\t\x12\x14\n\x0cisConfigured\x18\x02 \x01(\x08\x12\x10\n\x08toolName\x18\x03 \x01(\t\"~\n\x1bToolConfigStartOAuthRequest\x12\x42\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x34.com.augmentcode.rpc.ToolConfigStartOAuthRequestData:\x1b\xba\xe8t\x17tool-config-start-oauth\"2\n\x1fToolConfigStartOAuthRequestData\x12\x0f\n\x07\x61uthUrl\x18\x01 \x01(\t\"\x89\x01\n\x1cToolConfigStartOAuthResponse\x12\x43\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x35.com.augmentcode.rpc.ToolConfigStartOAuthResponseData:$\xba\xe8t tool-config-start-oauth-response\".\n ToolConfigStartOAuthResponseData\x12\n\n\x02ok\x18\x01 \x01(\x08\"\x84\x01\n\x1dToolConfigRevokeAccessRequest\x12\x44\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x36.com.augmentcode.rpc.ToolConfigRevokeAccessRequestData:\x1d\xba\xe8t\x19tool-config-revoke-access\"f\n!ToolConfigRevokeAccessRequestData\x12\x41\n\x06toolId\x18\x01 \x01(\x0b\x32\x31.com.augmentcode.sidecar.rpc.tools.ToolIdentifier\"8\n\x1aGetStoredMCPServersRequest:\x1a\xba\xe8t\x16get-stored-mcp-servers\"p\n\x1bGetStoredMCPServersResponse\x12,\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1e.com.augmentcode.rpc.MCPServer:#\xba\xe8t\x1fget-stored-mcp-servers-response\"f\n\x1aSetStoredMCPServersRequest\x12,\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1e.com.augmentcode.rpc.MCPServer:\x1a\xba\xe8t\x16set-stored-mcp-servers\"\xc5\x02\n\tMCPServer\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07\x63ommand\x18\x03 \x01(\t\x12\x11\n\targuments\x18\x04 \x01(\t\x12\x1d\n\x15useShellInterpolation\x18\x05 \x01(\x08\x12\x34\n\x03\x65nv\x18\x06 \x03(\x0b\x32\'.com.augmentcode.rpc.MCPServer.EnvEntry\x12\x10\n\x08\x64isabled\x18\x07 \x01(\x08\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x0b\n\x03url\x18\t \x01(\t\x12L\n\x05tools\x18\n \x03(\x0b\x32=.com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings\x1a*\n\x08\x45nvEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"C\n ExecuteInitialOrientationRequest:\x1f\xba\xe8t\x1b\x65xecute-initial-orientation\"R\n NavigateToSettingsSectionRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t: \xba\xe8t\x1cnavigate-to-settings-section\"G\n\x1bUpdateUserGuidelinesRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1a\xba\xe8t\x16update-user-guidelines\"\x1e\n\x0eSignOutRequest:\x0c\xba\xe8t\x08sign-out\"i\n\x17ShowNotificationRequest\x12\x37\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32).com.augmentcode.rpc.ShowNotificationData:\x15\xba\xe8t\x11show-notification\"C\n\x14ShowNotificationData\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x11\n\x04type\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_type\"E\n HandleTriggerImportDialogRequest:!\xba\xe8t\x1dtrigger-import-dialog-request\"\x91\x01\n!HandleTriggerImportDialogResponse\x12H\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32:.com.augmentcode.rpc.HandleTriggerImportDialogResponseData:\"\xba\xe8t\x1etrigger-import-dialog-response\">\n%HandleTriggerImportDialogResponseData\x12\x15\n\rselectedPaths\x18\x01 \x03(\t*-\n\nToolSafety\x12\n\n\x06UNSAFE\x10\x00\x12\x08\n\x04SAFE\x10\x01\x12\t\n\x05\x43HECK\x10\x02\x32\x9c\x0c\n\x16WebviewSettingsService\x12u\n\x10ToolConfigLoaded\x12,.com.augmentcode.rpc.ToolConfigLoadedRequest\x1a\x31.com.augmentcode.rpc.ToolConfigInitializeResponse\"\x00\x12\x86\x01\n\x18ToolConfigGetDefinitions\x12\x34.com.augmentcode.rpc.ToolConfigGetDefinitionsRequest\x1a\x32.com.augmentcode.rpc.ToolConfigDefinitionsResponse\"\x00\x12V\n\x0eToolConfigSave\x12*.com.augmentcode.rpc.ToolConfigSaveRequest\x1a\x16.google.protobuf.Empty\"\x00\x12}\n\x14ToolConfigStartOAuth\x12\x30.com.augmentcode.rpc.ToolConfigStartOAuthRequest\x1a\x31.com.augmentcode.rpc.ToolConfigStartOAuthResponse\"\x00\x12\x82\x01\n\x16ToolConfigRevokeAccess\x12\x32.com.augmentcode.rpc.ToolConfigRevokeAccessRequest\x1a\x32.com.augmentcode.rpc.ToolConfigDefinitionsResponse\"\x00\x12z\n\x13GetStoredMCPServers\x12/.com.augmentcode.rpc.GetStoredMCPServersRequest\x1a\x30.com.augmentcode.rpc.GetStoredMCPServersResponse\"\x00\x12`\n\x13SetStoredMCPServers\x12/.com.augmentcode.rpc.SetStoredMCPServersRequest\x1a\x16.google.protobuf.Empty\"\x00\x12l\n\x19\x45xecuteInitialOrientation\x12\x35.com.augmentcode.rpc.ExecuteInitialOrientationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\x62\n\x14UpdateUserGuidelines\x12\x30.com.augmentcode.rpc.UpdateUserGuidelinesRequest\x1a\x16.google.protobuf.Empty\"\x00\x12H\n\x07SignOut\x12#.com.augmentcode.rpc.SignOutRequest\x1a\x16.google.protobuf.Empty\"\x00\x12J\n\x08OpenFile\x12$.com.augmentcode.rpc.OpenFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12Z\n\x10ShowNotification\x12,.com.augmentcode.rpc.ShowNotificationRequest\x1a\x16.google.protobuf.Empty\"\x00\x12u\n\x15OpenConfirmationModal\x12*.com.augmentcode.rpc.OpenConfirmationModal\x1a..com.augmentcode.rpc.ConfirmationModalResponse\"\x00\x12\x8c\x01\n\x19HandleTriggerImportDialog\x12\x35.com.augmentcode.rpc.HandleTriggerImportDialogRequest\x1a\x36.com.augmentcode.rpc.HandleTriggerImportDialogResponse\"\x00\x42&\n\x13\x63om.augmentcode.rpcB\rSettingsTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'intellij_settings_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.augmentcode.rpcB\rSettingsTypesP\001'
  _globals['_SETTINGSPANELLOADED']._loaded_options = None
  _globals['_SETTINGSPANELLOADED']._serialized_options = b'\272\350t\025settings-panel-loaded'
  _globals['_TOOLCONFIGLOADEDREQUEST']._loaded_options = None
  _globals['_TOOLCONFIGLOADEDREQUEST']._serialized_options = b'\272\350t\022tool-config-loaded'
  _globals['_TOOLCONFIGINITIALIZERESPONSE']._loaded_options = None
  _globals['_TOOLCONFIGINITIALIZERESPONSE']._serialized_options = b'\272\350t\026tool-config-initialize'
  _globals['_TOOLCONFIGGETDEFINITIONSREQUEST']._loaded_options = None
  _globals['_TOOLCONFIGGETDEFINITIONSREQUEST']._serialized_options = b'\272\350t\033tool-config-get-definitions'
  _globals['_TOOLCONFIGDEFINITIONSRESPONSE']._loaded_options = None
  _globals['_TOOLCONFIGDEFINITIONSRESPONSE']._serialized_options = b'\272\350t tool-config-definitions-response'
  _globals['_TOOLCONFIGSAVEREQUEST']._loaded_options = None
  _globals['_TOOLCONFIGSAVEREQUEST']._serialized_options = b'\272\350t\020tool-config-save'
  _globals['_TOOLCONFIGSTARTOAUTHREQUEST']._loaded_options = None
  _globals['_TOOLCONFIGSTARTOAUTHREQUEST']._serialized_options = b'\272\350t\027tool-config-start-oauth'
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSE']._loaded_options = None
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSE']._serialized_options = b'\272\350t tool-config-start-oauth-response'
  _globals['_TOOLCONFIGREVOKEACCESSREQUEST']._loaded_options = None
  _globals['_TOOLCONFIGREVOKEACCESSREQUEST']._serialized_options = b'\272\350t\031tool-config-revoke-access'
  _globals['_GETSTOREDMCPSERVERSREQUEST']._loaded_options = None
  _globals['_GETSTOREDMCPSERVERSREQUEST']._serialized_options = b'\272\350t\026get-stored-mcp-servers'
  _globals['_GETSTOREDMCPSERVERSRESPONSE']._loaded_options = None
  _globals['_GETSTOREDMCPSERVERSRESPONSE']._serialized_options = b'\272\350t\037get-stored-mcp-servers-response'
  _globals['_SETSTOREDMCPSERVERSREQUEST']._loaded_options = None
  _globals['_SETSTOREDMCPSERVERSREQUEST']._serialized_options = b'\272\350t\026set-stored-mcp-servers'
  _globals['_MCPSERVER_ENVENTRY']._loaded_options = None
  _globals['_MCPSERVER_ENVENTRY']._serialized_options = b'8\001'
  _globals['_EXECUTEINITIALORIENTATIONREQUEST']._loaded_options = None
  _globals['_EXECUTEINITIALORIENTATIONREQUEST']._serialized_options = b'\272\350t\033execute-initial-orientation'
  _globals['_NAVIGATETOSETTINGSSECTIONREQUEST']._loaded_options = None
  _globals['_NAVIGATETOSETTINGSSECTIONREQUEST']._serialized_options = b'\272\350t\034navigate-to-settings-section'
  _globals['_UPDATEUSERGUIDELINESREQUEST']._loaded_options = None
  _globals['_UPDATEUSERGUIDELINESREQUEST']._serialized_options = b'\272\350t\026update-user-guidelines'
  _globals['_SIGNOUTREQUEST']._loaded_options = None
  _globals['_SIGNOUTREQUEST']._serialized_options = b'\272\350t\010sign-out'
  _globals['_SHOWNOTIFICATIONREQUEST']._loaded_options = None
  _globals['_SHOWNOTIFICATIONREQUEST']._serialized_options = b'\272\350t\021show-notification'
  _globals['_HANDLETRIGGERIMPORTDIALOGREQUEST']._loaded_options = None
  _globals['_HANDLETRIGGERIMPORTDIALOGREQUEST']._serialized_options = b'\272\350t\035trigger-import-dialog-request'
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSE']._loaded_options = None
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSE']._serialized_options = b'\272\350t\036trigger-import-dialog-response'
  _globals['_TOOLSAFETY']._serialized_start=3680
  _globals['_TOOLSAFETY']._serialized_end=3725
  _globals['_SETTINGSPANELLOADED']._serialized_start=190
  _globals['_SETTINGSPANELLOADED']._serialized_end=238
  _globals['_TOOLCONFIGLOADEDREQUEST']._serialized_start=240
  _globals['_TOOLCONFIGLOADEDREQUEST']._serialized_end=289
  _globals['_TOOLCONFIGINITIALIZERESPONSE']._serialized_start=291
  _globals['_TOOLCONFIGINITIALIZERESPONSE']._serialized_end=416
  _globals['_SETTINGSCOMPONENTSUPPORTED']._serialized_start=419
  _globals['_SETTINGSCOMPONENTSUPPORTED']._serialized_end=620
  _globals['_SETTINGSINITIALIZERESPONSEDATA']._serialized_start=623
  _globals['_SETTINGSINITIALIZERESPONSEDATA']._serialized_end=1025
  _globals['_TOOLSETTINGS']._serialized_start=1027
  _globals['_TOOLSETTINGS']._serialized_end=1093
  _globals['_TOOLCONFIGGETDEFINITIONSREQUEST']._serialized_start=1096
  _globals['_TOOLCONFIGGETDEFINITIONSREQUEST']._serialized_end=1234
  _globals['_TOOLCONFIGGETDEFINITIONSREQUESTDATA']._serialized_start=1236
  _globals['_TOOLCONFIGGETDEFINITIONSREQUESTDATA']._serialized_end=1291
  _globals['_TOOLCONFIGDEFINITIONSRESPONSE']._serialized_start=1294
  _globals['_TOOLCONFIGDEFINITIONSRESPONSE']._serialized_end=1433
  _globals['_TOOLCONFIGDEFINITIONSRESPONSEDATA']._serialized_start=1435
  _globals['_TOOLCONFIGDEFINITIONSRESPONSEDATA']._serialized_end=1552
  _globals['_TOOLCONFIGSAVEREQUEST']._serialized_start=1554
  _globals['_TOOLCONFIGSAVEREQUEST']._serialized_end=1661
  _globals['_TOOLCONFIGSAVEREQUESTDATA']._serialized_start=1663
  _globals['_TOOLCONFIGSAVEREQUESTDATA']._serialized_end=1750
  _globals['_TOOLCONFIGSTARTOAUTHREQUEST']._serialized_start=1752
  _globals['_TOOLCONFIGSTARTOAUTHREQUEST']._serialized_end=1878
  _globals['_TOOLCONFIGSTARTOAUTHREQUESTDATA']._serialized_start=1880
  _globals['_TOOLCONFIGSTARTOAUTHREQUESTDATA']._serialized_end=1930
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSE']._serialized_start=1933
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSE']._serialized_end=2070
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSEDATA']._serialized_start=2072
  _globals['_TOOLCONFIGSTARTOAUTHRESPONSEDATA']._serialized_end=2118
  _globals['_TOOLCONFIGREVOKEACCESSREQUEST']._serialized_start=2121
  _globals['_TOOLCONFIGREVOKEACCESSREQUEST']._serialized_end=2253
  _globals['_TOOLCONFIGREVOKEACCESSREQUESTDATA']._serialized_start=2255
  _globals['_TOOLCONFIGREVOKEACCESSREQUESTDATA']._serialized_end=2357
  _globals['_GETSTOREDMCPSERVERSREQUEST']._serialized_start=2359
  _globals['_GETSTOREDMCPSERVERSREQUEST']._serialized_end=2415
  _globals['_GETSTOREDMCPSERVERSRESPONSE']._serialized_start=2417
  _globals['_GETSTOREDMCPSERVERSRESPONSE']._serialized_end=2529
  _globals['_SETSTOREDMCPSERVERSREQUEST']._serialized_start=2531
  _globals['_SETSTOREDMCPSERVERSREQUEST']._serialized_end=2633
  _globals['_MCPSERVER']._serialized_start=2636
  _globals['_MCPSERVER']._serialized_end=2961
  _globals['_MCPSERVER_ENVENTRY']._serialized_start=2919
  _globals['_MCPSERVER_ENVENTRY']._serialized_end=2961
  _globals['_EXECUTEINITIALORIENTATIONREQUEST']._serialized_start=2963
  _globals['_EXECUTEINITIALORIENTATIONREQUEST']._serialized_end=3030
  _globals['_NAVIGATETOSETTINGSSECTIONREQUEST']._serialized_start=3032
  _globals['_NAVIGATETOSETTINGSSECTIONREQUEST']._serialized_end=3114
  _globals['_UPDATEUSERGUIDELINESREQUEST']._serialized_start=3116
  _globals['_UPDATEUSERGUIDELINESREQUEST']._serialized_end=3187
  _globals['_SIGNOUTREQUEST']._serialized_start=3189
  _globals['_SIGNOUTREQUEST']._serialized_end=3219
  _globals['_SHOWNOTIFICATIONREQUEST']._serialized_start=3221
  _globals['_SHOWNOTIFICATIONREQUEST']._serialized_end=3326
  _globals['_SHOWNOTIFICATIONDATA']._serialized_start=3328
  _globals['_SHOWNOTIFICATIONDATA']._serialized_end=3395
  _globals['_HANDLETRIGGERIMPORTDIALOGREQUEST']._serialized_start=3397
  _globals['_HANDLETRIGGERIMPORTDIALOGREQUEST']._serialized_end=3466
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSE']._serialized_start=3469
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSE']._serialized_end=3614
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSEDATA']._serialized_start=3616
  _globals['_HANDLETRIGGERIMPORTDIALOGRESPONSEDATA']._serialized_end=3678
  _globals['_WEBVIEWSETTINGSSERVICE']._serialized_start=3728
  _globals['_WEBVIEWSETTINGSSERVICE']._serialized_end=5292
# @@protoc_insertion_point(module_scope)
