# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-client.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'api-client.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import chat_pb2 as chat__pb2
import tools_pb2 as tools__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x61pi-client.proto\x12,com.augmentcode.sidecar.rpc.clientInterfaces\x1a\nchat.proto\x1a\x0btools.proto\"\x9c\x01\n\x1d\x41gentCodebaseRetrievalRequest\x12\x1a\n\x12informationRequest\x18\x01 \x01(\t\x12\x46\n\x0b\x63hatHistory\x18\x02 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\x17\n\x0fmaxOutputLength\x18\x03 \x01(\x05\"<\n\x1e\x41gentCodebaseRetrievalResponse\x12\x1a\n\x12\x66ormattedRetrieval\x18\x01 \x01(\t\"p\n\x16\x43heckToolSafetyRequest\x12?\n\x06toolId\x18\x01 \x01(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\x12\x15\n\rtoolInputJson\x18\x02 \x01(\t\")\n\x17\x43heckToolSafetyResponse\x12\x0e\n\x06isSafe\x18\x01 \x01(\x08\"g\n\x14LogAgentSessionEvent\x12O\n\x06\x65vents\x18\x01 \x03(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.AgentSessionEvent\"\xd2\x01\n\x11\x41gentSessionEvent\x12\x16\n\x0e\x65vent_time_sec\x18\x01 \x01(\x05\x12\x17\n\x0f\x65vent_time_nsec\x18\x02 \x01(\x05\x12\x12\n\nevent_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63onversation_id\x18\x04 \x01(\t\x12P\n\nevent_data\x18\x05 \x01(\x0b\x32\x37.com.augmentcode.sidecar.rpc.clientInterfaces.EventDataH\x00\x88\x01\x01\x42\r\n\x0b_event_data\"\xac\x06\n\tEventData\x12i\n\x17\x61gent_interruption_data\x18\x02 \x01(\x0b\x32\x43.com.augmentcode.sidecar.rpc.clientInterfaces.AgentInterruptionDataH\x00\x88\x01\x01\x12h\n\x17remember_tool_call_data\x18\x03 \x01(\x0b\x32\x42.com.augmentcode.sidecar.rpc.clientInterfaces.RememberToolCallDataH\x01\x88\x01\x01\x12h\n\x17memories_file_open_data\x18\x04 \x01(\x0b\x32\x42.com.augmentcode.sidecar.rpc.clientInterfaces.MemoriesFileOpenDataH\x02\x88\x01\x01\x12k\n\x18initial_orientation_data\x18\x05 \x01(\x0b\x32\x44.com.augmentcode.sidecar.rpc.clientInterfaces.InitialOrientationDataH\x03\x88\x01\x01\x12h\n\x19\x63lassify_and_distill_data\x18\x06 \x01(\x0b\<EMAIL>\x04\x88\x01\x01\x12\x62\n\x13\x66lush_memories_data\x18\x07 \x01(\x0b\<EMAIL>\x05\x88\x01\x01\x42\x1a\n\x18_agent_interruption_dataB\x1a\n\x18_remember_tool_call_dataB\x1a\n\x18_memories_file_open_dataB\x1b\n\x19_initial_orientation_dataB\x1c\n\x1a_classify_and_distill_dataB\x16\n\x14_flush_memories_data\"\x9b\x01\n\x14RememberToolCallData\x12\x0e\n\x06\x63\x61ller\x18\x01 \x01(\x05\x12\x1d\n\x15is_complex_new_memory\x18\x02 \x01(\x08\x12T\n\x0ctracing_data\x18\x03 \x01(\x0b\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData\"\xc1\x06\n\x10\x41gentTracingData\x12X\n\x05\x66lags\x18\x01 \x03(\x0b\x32I.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData.FlagsEntry\x12V\n\x04nums\x18\x02 \x03(\x0b\x32H.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData.NumsEntry\x12\x65\n\x0cstring_stats\x18\x03 \x03(\x0b\x32O.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData.StringStatsEntry\x12\x63\n\x0brequest_ids\x18\x04 \x03(\x0b\x32N.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData.RequestIdsEntry\x1a\x65\n\nFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x46\n\x05value\x18\x02 \x01(\x0b\x32\x37.com.augmentcode.sidecar.rpc.clientInterfaces.TimedBool:\x02\x38\x01\x1a\x66\n\tNumsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12H\n\x05value\x18\x02 \x01(\x0b\x32\x39.com.augmentcode.sidecar.rpc.clientInterfaces.TimedNumber:\x02\x38\x01\x1ar\n\x10StringStatsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12M\n\x05value\x18\x02 \x01(\x0b\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.TimedStringStats:\x02\x38\x01\x1al\n\x0fRequestIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12H\n\x05value\x18\x02 \x01(\x0b\x32\x39.com.augmentcode.sidecar.rpc.clientInterfaces.TimedString:\x02\x38\x01\"-\n\tTimedBool\x12\r\n\x05value\x18\x01 \x01(\x08\x12\x11\n\ttimestamp\x18\x02 \x01(\t\"/\n\x0bTimedNumber\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x11\n\ttimestamp\x18\x02 \x01(\t\"o\n\x10TimedStringStats\x12H\n\x05value\x18\x01 \x01(\x0b\x32\x39.com.augmentcode.sidecar.rpc.clientInterfaces.StringStats\x12\x11\n\ttimestamp\x18\x02 \x01(\t\"/\n\x0bTimedString\x12\r\n\x05value\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\t\"3\n\x0bStringStats\x12\x11\n\tnum_lines\x18\x01 \x01(\x05\x12\x11\n\tnum_chars\x18\x02 \x01(\x05\"X\n\x14MemoriesFileOpenData\x12$\n\x17memories_path_undefined\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\x1a\n\x18_memories_path_undefined\"~\n\x16InitialOrientationData\x12\x0e\n\x06\x63\x61ller\x18\x01 \x01(\x05\x12T\n\x0ctracing_data\x18\x02 \x01(\x0b\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData\"j\n\x12GenericTracingData\x12T\n\x0ctracing_data\x18\x01 \x01(\x0b\x32>.com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData\"M\n\x15\x41gentInterruptionData\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12 \n\x18\x63urr_conversation_length\x18\x02 \x01(\x05\"\xcd\x02\n\x1c\x43hatHistorySummarizationData\x12 \n\x18total_history_char_count\x18\x01 \x01(\x05\x12$\n\x1ctotal_history_exchange_count\x18\x02 \x01(\x05\x12\x17\n\x0fhead_char_count\x18\x03 \x01(\x05\x12\x1b\n\x13head_exchange_count\x18\x04 \x01(\x05\x12\x1c\n\x14head_last_request_id\x18\x05 \x01(\t\x12\x17\n\x0ftail_char_count\x18\x06 \x01(\x05\x12\x1b\n\x13tail_exchange_count\x18\x07 \x01(\x05\x12\x1c\n\x14tail_last_request_id\x18\x08 \x01(\t\x12\x1a\n\x12summary_char_count\x18\t \x01(\x05\x12!\n\x19summarization_duration_ms\x18\n \x01(\x05\"\xb5\x01\n\x15\x41gentRequestEventData\x12x\n\x1f\x63hat_history_summarization_data\x18\x01 \x01(\x0b\x32J.com.augmentcode.sidecar.rpc.clientInterfaces.ChatHistorySummarizationDataH\x00\x88\x01\x01\x42\"\n _chat_history_summarization_data\"g\n\x14LogAgentRequestEvent\x12O\n\x06\x65vents\x18\x01 \x03(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEvent\"\x8f\x02\n\x11\x41gentRequestEvent\x12\x16\n\x0e\x65vent_time_sec\x18\x01 \x01(\x05\x12\x17\n\x0f\x65vent_time_nsec\x18\x02 \x01(\x05\x12\x12\n\nevent_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63onversation_id\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\t\x12\x1b\n\x13\x63hat_history_length\x18\x06 \x01(\x05\x12\\\n\nevent_data\x18\x07 \x01(\x0b\x32\x43.com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEventDataH\x00\x88\x01\x01\x42\r\n\x0b_event_dataBC\n,com.augmentcode.sidecar.rpc.clientInterfacesB\x11\x41PIClientRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_client_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n,com.augmentcode.sidecar.rpc.clientInterfacesB\021APIClientRPCTypesP\001'
  _globals['_AGENTTRACINGDATA_FLAGSENTRY']._loaded_options = None
  _globals['_AGENTTRACINGDATA_FLAGSENTRY']._serialized_options = b'8\001'
  _globals['_AGENTTRACINGDATA_NUMSENTRY']._loaded_options = None
  _globals['_AGENTTRACINGDATA_NUMSENTRY']._serialized_options = b'8\001'
  _globals['_AGENTTRACINGDATA_STRINGSTATSENTRY']._loaded_options = None
  _globals['_AGENTTRACINGDATA_STRINGSTATSENTRY']._serialized_options = b'8\001'
  _globals['_AGENTTRACINGDATA_REQUESTIDSENTRY']._loaded_options = None
  _globals['_AGENTTRACINGDATA_REQUESTIDSENTRY']._serialized_options = b'8\001'
  _globals['_AGENTCODEBASERETRIEVALREQUEST']._serialized_start=92
  _globals['_AGENTCODEBASERETRIEVALREQUEST']._serialized_end=248
  _globals['_AGENTCODEBASERETRIEVALRESPONSE']._serialized_start=250
  _globals['_AGENTCODEBASERETRIEVALRESPONSE']._serialized_end=310
  _globals['_CHECKTOOLSAFETYREQUEST']._serialized_start=312
  _globals['_CHECKTOOLSAFETYREQUEST']._serialized_end=424
  _globals['_CHECKTOOLSAFETYRESPONSE']._serialized_start=426
  _globals['_CHECKTOOLSAFETYRESPONSE']._serialized_end=467
  _globals['_LOGAGENTSESSIONEVENT']._serialized_start=469
  _globals['_LOGAGENTSESSIONEVENT']._serialized_end=572
  _globals['_AGENTSESSIONEVENT']._serialized_start=575
  _globals['_AGENTSESSIONEVENT']._serialized_end=785
  _globals['_EVENTDATA']._serialized_start=788
  _globals['_EVENTDATA']._serialized_end=1600
  _globals['_REMEMBERTOOLCALLDATA']._serialized_start=1603
  _globals['_REMEMBERTOOLCALLDATA']._serialized_end=1758
  _globals['_AGENTTRACINGDATA']._serialized_start=1761
  _globals['_AGENTTRACINGDATA']._serialized_end=2594
  _globals['_AGENTTRACINGDATA_FLAGSENTRY']._serialized_start=2163
  _globals['_AGENTTRACINGDATA_FLAGSENTRY']._serialized_end=2264
  _globals['_AGENTTRACINGDATA_NUMSENTRY']._serialized_start=2266
  _globals['_AGENTTRACINGDATA_NUMSENTRY']._serialized_end=2368
  _globals['_AGENTTRACINGDATA_STRINGSTATSENTRY']._serialized_start=2370
  _globals['_AGENTTRACINGDATA_STRINGSTATSENTRY']._serialized_end=2484
  _globals['_AGENTTRACINGDATA_REQUESTIDSENTRY']._serialized_start=2486
  _globals['_AGENTTRACINGDATA_REQUESTIDSENTRY']._serialized_end=2594
  _globals['_TIMEDBOOL']._serialized_start=2596
  _globals['_TIMEDBOOL']._serialized_end=2641
  _globals['_TIMEDNUMBER']._serialized_start=2643
  _globals['_TIMEDNUMBER']._serialized_end=2690
  _globals['_TIMEDSTRINGSTATS']._serialized_start=2692
  _globals['_TIMEDSTRINGSTATS']._serialized_end=2803
  _globals['_TIMEDSTRING']._serialized_start=2805
  _globals['_TIMEDSTRING']._serialized_end=2852
  _globals['_STRINGSTATS']._serialized_start=2854
  _globals['_STRINGSTATS']._serialized_end=2905
  _globals['_MEMORIESFILEOPENDATA']._serialized_start=2907
  _globals['_MEMORIESFILEOPENDATA']._serialized_end=2995
  _globals['_INITIALORIENTATIONDATA']._serialized_start=2997
  _globals['_INITIALORIENTATIONDATA']._serialized_end=3123
  _globals['_GENERICTRACINGDATA']._serialized_start=3125
  _globals['_GENERICTRACINGDATA']._serialized_end=3231
  _globals['_AGENTINTERRUPTIONDATA']._serialized_start=3233
  _globals['_AGENTINTERRUPTIONDATA']._serialized_end=3310
  _globals['_CHATHISTORYSUMMARIZATIONDATA']._serialized_start=3313
  _globals['_CHATHISTORYSUMMARIZATIONDATA']._serialized_end=3646
  _globals['_AGENTREQUESTEVENTDATA']._serialized_start=3649
  _globals['_AGENTREQUESTEVENTDATA']._serialized_end=3830
  _globals['_LOGAGENTREQUESTEVENT']._serialized_start=3832
  _globals['_LOGAGENTREQUESTEVENT']._serialized_end=3935
  _globals['_AGENTREQUESTEVENT']._serialized_start=3938
  _globals['_AGENTREQUESTEVENT']._serialized_end=4209
# @@protoc_insertion_point(module_scope)
