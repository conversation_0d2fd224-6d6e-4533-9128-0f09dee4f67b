syntax = "proto3";

package com.augmentcode.rpc;

import "google/protobuf/empty.proto";

// 简化版的Augment聊天服务
service WebviewChatService {
  rpc ChatUserMessage(ChatUserMessageRequest) returns (stream ChatModelReply) {}
  rpc ChatUserCancel(ChatUserCancelRequest) returns (google.protobuf.Empty) {}
}

message ChatUserMessageRequest {
  string message = 1;
  string conversation_id = 2;
  string request_id = 3;
}

message ChatModelReply {
  string text = 1;
  string request_id = 2;
  bool streaming = 3;
  bool is_complete = 4;
}

message ChatUserCancelRequest {
  string request_id = 1;
}
