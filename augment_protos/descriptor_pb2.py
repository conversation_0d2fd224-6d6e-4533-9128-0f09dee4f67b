# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: descriptor.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'descriptor.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x64\x65scriptor.proto\x12\x0fgoogle.protobuf\"G\n\x11\x46ileDescriptorSet\x12\x32\n\x04\x66ile\x18\x01 \x03(\x0b\x32$.google.protobuf.FileDescriptorProto\"\xec\x03\n\x13\x46ileDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07package\x18\x02 \x01(\t\x12\x12\n\ndependency\x18\x03 \x03(\t\x12\x19\n\x11public_dependency\x18\n \x03(\x05\x12\x17\n\x0fweak_dependency\x18\x0b \x03(\x05\x12\x36\n\x0cmessage_type\x18\x04 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x05 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12\x38\n\x07service\x18\x06 \x03(\x0b\x32\'.google.protobuf.ServiceDescriptorProto\x12\x38\n\textension\x18\x07 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12-\n\x07options\x18\x08 \x01(\x0b\x32\x1c.google.protobuf.FileOptions\x12\x39\n\x10source_code_info\x18\t \x01(\x0b\x32\x1f.google.protobuf.SourceCodeInfo\x12\x0e\n\x06syntax\x18\x0c \x01(\t\x12\x0f\n\x07\x65\x64ition\x18\r \x01(\t\"\xa9\x05\n\x0f\x44\x65scriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x34\n\x05\x66ield\x18\x02 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x38\n\textension\x18\x06 \x03(\x0b\x32%.google.protobuf.FieldDescriptorProto\x12\x35\n\x0bnested_type\x18\x03 \x03(\x0b\x32 .google.protobuf.DescriptorProto\x12\x37\n\tenum_type\x18\x04 \x03(\x0b\x32$.google.protobuf.EnumDescriptorProto\x12H\n\x0f\x65xtension_range\x18\x05 \x03(\x0b\x32/.google.protobuf.DescriptorProto.ExtensionRange\x12\x39\n\noneof_decl\x18\x08 \x03(\x0b\x32%.google.protobuf.OneofDescriptorProto\x12\x30\n\x07options\x18\x07 \x01(\x0b\x32\x1f.google.protobuf.MessageOptions\x12\x46\n\x0ereserved_range\x18\t \x03(\x0b\x32..google.protobuf.DescriptorProto.ReservedRange\x12\x15\n\rreserved_name\x18\n \x03(\t\x1a\x65\n\x0e\x45xtensionRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\x12\x37\n\x07options\x18\x03 \x01(\x0b\x32&.google.protobuf.ExtensionRangeOptions\x1a+\n\rReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"\xe0\x03\n\x15\x45xtensionRangeOptions\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\x12L\n\x0b\x64\x65\x63laration\x18\x02 \x03(\x0b\x32\x32.google.protobuf.ExtensionRangeOptions.DeclarationB\x03\x88\x01\x02\x12-\n\x08\x66\x65\x61tures\x18\x32 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12Z\n\x0cverification\x18\x03 \x01(\x0e\x32\x38.google.protobuf.ExtensionRangeOptions.VerificationState:\nUNVERIFIED\x1ah\n\x0b\x44\x65\x63laration\x12\x0e\n\x06number\x18\x01 \x01(\x05\x12\x11\n\tfull_name\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08reserved\x18\x05 \x01(\x08\x12\x10\n\x08repeated\x18\x06 \x01(\x08J\x04\x08\x04\x10\x05\"4\n\x11VerificationState\x12\x0f\n\x0b\x44\x45\x43LARATION\x10\x00\x12\x0e\n\nUNVERIFIED\x10\x01*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xd5\x05\n\x14\x46ieldDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x03 \x01(\x05\x12:\n\x05label\x18\x04 \x01(\x0e\x32+.google.protobuf.FieldDescriptorProto.Label\x12\x38\n\x04type\x18\x05 \x01(\x0e\x32*.google.protobuf.FieldDescriptorProto.Type\x12\x11\n\ttype_name\x18\x06 \x01(\t\x12\x10\n\x08\x65xtendee\x18\x02 \x01(\t\x12\x15\n\rdefault_value\x18\x07 \x01(\t\x12\x13\n\x0boneof_index\x18\t \x01(\x05\x12\x11\n\tjson_name\x18\n \x01(\t\x12.\n\x07options\x18\x08 \x01(\x0b\x32\x1d.google.protobuf.FieldOptions\x12\x17\n\x0fproto3_optional\x18\x11 \x01(\x08\"\xb6\x02\n\x04Type\x12\x0f\n\x0bTYPE_DOUBLE\x10\x01\x12\x0e\n\nTYPE_FLOAT\x10\x02\x12\x0e\n\nTYPE_INT64\x10\x03\x12\x0f\n\x0bTYPE_UINT64\x10\x04\x12\x0e\n\nTYPE_INT32\x10\x05\x12\x10\n\x0cTYPE_FIXED64\x10\x06\x12\x10\n\x0cTYPE_FIXED32\x10\x07\x12\r\n\tTYPE_BOOL\x10\x08\x12\x0f\n\x0bTYPE_STRING\x10\t\x12\x0e\n\nTYPE_GROUP\x10\n\x12\x10\n\x0cTYPE_MESSAGE\x10\x0b\x12\x0e\n\nTYPE_BYTES\x10\x0c\x12\x0f\n\x0bTYPE_UINT32\x10\r\x12\r\n\tTYPE_ENUM\x10\x0e\x12\x11\n\rTYPE_SFIXED32\x10\x0f\x12\x11\n\rTYPE_SFIXED64\x10\x10\x12\x0f\n\x0bTYPE_SINT32\x10\x11\x12\x0f\n\x0bTYPE_SINT64\x10\x12\"C\n\x05Label\x12\x12\n\x0eLABEL_OPTIONAL\x10\x01\x12\x12\n\x0eLABEL_REQUIRED\x10\x02\x12\x12\n\x0eLABEL_REPEATED\x10\x03\"T\n\x14OneofDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x07options\x18\x02 \x01(\x0b\x32\x1d.google.protobuf.OneofOptions\"\xa4\x02\n\x13\x45numDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x38\n\x05value\x18\x02 \x03(\x0b\x32).google.protobuf.EnumValueDescriptorProto\x12-\n\x07options\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.EnumOptions\x12N\n\x0ereserved_range\x18\x04 \x03(\x0b\x32\x36.google.protobuf.EnumDescriptorProto.EnumReservedRange\x12\x15\n\rreserved_name\x18\x05 \x03(\t\x1a/\n\x11\x45numReservedRange\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x05\"l\n\x18\x45numValueDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06number\x18\x02 \x01(\x05\x12\x32\n\x07options\x18\x03 \x01(\x0b\x32!.google.protobuf.EnumValueOptions\"\x90\x01\n\x16ServiceDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x36\n\x06method\x18\x02 \x03(\x0b\x32&.google.protobuf.MethodDescriptorProto\x12\x30\n\x07options\x18\x03 \x01(\x0b\x32\x1f.google.protobuf.ServiceOptions\"\xc1\x01\n\x15MethodDescriptorProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ninput_type\x18\x02 \x01(\t\x12\x13\n\x0boutput_type\x18\x03 \x01(\t\x12/\n\x07options\x18\x04 \x01(\x0b\x32\x1e.google.protobuf.MethodOptions\x12\x1f\n\x10\x63lient_streaming\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10server_streaming\x18\x06 \x01(\x08:\x05\x66\x61lse\"\xd4\x06\n\x0b\x46ileOptions\x12\x14\n\x0cjava_package\x18\x01 \x01(\t\x12\x1c\n\x14java_outer_classname\x18\x08 \x01(\t\x12\"\n\x13java_multiple_files\x18\n \x01(\x08:\x05\x66\x61lse\x12)\n\x1djava_generate_equals_and_hash\x18\x14 \x01(\x08\x42\x02\x18\x01\x12%\n\x16java_string_check_utf8\x18\x1b \x01(\x08:\x05\x66\x61lse\x12\x46\n\x0coptimize_for\x18\t \x01(\x0e\x32).google.protobuf.FileOptions.OptimizeMode:\x05SPEED\x12\x12\n\ngo_package\x18\x0b \x01(\t\x12\"\n\x13\x63\x63_generic_services\x18\x10 \x01(\x08:\x05\x66\x61lse\x12$\n\x15java_generic_services\x18\x11 \x01(\x08:\x05\x66\x61lse\x12\"\n\x13py_generic_services\x18\x12 \x01(\x08:\x05\x66\x61lse\x12#\n\x14php_generic_services\x18* \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x17 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x10\x63\x63_enable_arenas\x18\x1f \x01(\x08:\x04true\x12\x19\n\x11objc_class_prefix\x18$ \x01(\t\x12\x18\n\x10\x63sharp_namespace\x18% \x01(\t\x12\x14\n\x0cswift_prefix\x18\' \x01(\t\x12\x18\n\x10php_class_prefix\x18( \x01(\t\x12\x15\n\rphp_namespace\x18) \x01(\t\x12\x1e\n\x16php_metadata_namespace\x18, \x01(\t\x12\x14\n\x0cruby_package\x18- \x01(\t\x12-\n\x08\x66\x65\x61tures\x18\x32 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\":\n\x0cOptimizeMode\x12\t\n\x05SPEED\x10\x01\x12\r\n\tCODE_SIZE\x10\x02\x12\x10\n\x0cLITE_RUNTIME\x10\x03*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08&\x10\'\"\xe7\x02\n\x0eMessageOptions\x12&\n\x17message_set_wire_format\x18\x01 \x01(\x08:\x05\x66\x61lse\x12.\n\x1fno_standard_descriptor_accessor\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x11\n\tmap_entry\x18\x07 \x01(\x08\x12\x32\n&deprecated_legacy_json_field_conflicts\x18\x0b \x01(\x08\x42\x02\x18\x01\x12-\n\x08\x66\x65\x61tures\x18\x0c \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n\"\xf3\x08\n\x0c\x46ieldOptions\x12:\n\x05\x63type\x18\x01 \x01(\x0e\x32#.google.protobuf.FieldOptions.CType:\x06STRING\x12\x0e\n\x06packed\x18\x02 \x01(\x08\x12?\n\x06jstype\x18\x06 \x01(\x0e\x32$.google.protobuf.FieldOptions.JSType:\tJS_NORMAL\x12\x13\n\x04lazy\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0funverified_lazy\x18\x0f \x01(\x08:\x05\x66\x61lse\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x13\n\x04weak\x18\n \x01(\x08:\x05\x66\x61lse\x12\x1b\n\x0c\x64\x65\x62ug_redact\x18\x10 \x01(\x08:\x05\x66\x61lse\x12@\n\tretention\x18\x11 \x01(\x0e\x32-.google.protobuf.FieldOptions.OptionRetention\x12?\n\x07targets\x18\x13 \x03(\x0e\x32..google.protobuf.FieldOptions.OptionTargetType\x12\x46\n\x10\x65\x64ition_defaults\x18\x14 \x03(\x0b\x32,.google.protobuf.FieldOptions.EditionDefault\x12-\n\x08\x66\x65\x61tures\x18\x15 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\x1a\x30\n\x0e\x45\x64itionDefault\x12\x0f\n\x07\x65\x64ition\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"/\n\x05\x43Type\x12\n\n\x06STRING\x10\x00\x12\x08\n\x04\x43ORD\x10\x01\x12\x10\n\x0cSTRING_PIECE\x10\x02\"5\n\x06JSType\x12\r\n\tJS_NORMAL\x10\x00\x12\r\n\tJS_STRING\x10\x01\x12\r\n\tJS_NUMBER\x10\x02\"U\n\x0fOptionRetention\x12\x15\n\x11RETENTION_UNKNOWN\x10\x00\x12\x15\n\x11RETENTION_RUNTIME\x10\x01\x12\x14\n\x10RETENTION_SOURCE\x10\x02\"\x8c\x02\n\x10OptionTargetType\x12\x17\n\x13TARGET_TYPE_UNKNOWN\x10\x00\x12\x14\n\x10TARGET_TYPE_FILE\x10\x01\x12\x1f\n\x1bTARGET_TYPE_EXTENSION_RANGE\x10\x02\x12\x17\n\x13TARGET_TYPE_MESSAGE\x10\x03\x12\x15\n\x11TARGET_TYPE_FIELD\x10\x04\x12\x15\n\x11TARGET_TYPE_ONEOF\x10\x05\x12\x14\n\x10TARGET_TYPE_ENUM\x10\x06\x12\x1a\n\x16TARGET_TYPE_ENUM_ENTRY\x10\x07\x12\x17\n\x13TARGET_TYPE_SERVICE\x10\x08\x12\x16\n\x12TARGET_TYPE_METHOD\x10\t*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x04\x10\x05J\x04\x08\x12\x10\x13\"\x8d\x01\n\x0cOneofOptions\x12-\n\x08\x66\x65\x61tures\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xf6\x01\n\x0b\x45numOptions\x12\x13\n\x0b\x61llow_alias\x18\x02 \x01(\x08\x12\x19\n\ndeprecated\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x32\n&deprecated_legacy_json_field_conflicts\x18\x06 \x01(\x08\x42\x02\x18\x01\x12-\n\x08\x66\x65\x61tures\x18\x07 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02J\x04\x08\x05\x10\x06\"\xc9\x01\n\x10\x45numValueOptions\x12\x19\n\ndeprecated\x18\x01 \x01(\x08:\x05\x66\x61lse\x12-\n\x08\x66\x65\x61tures\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x1b\n\x0c\x64\x65\x62ug_redact\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xaa\x01\n\x0eServiceOptions\x12-\n\x08\x66\x65\x61tures\x18\" \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\xdc\x02\n\rMethodOptions\x12\x19\n\ndeprecated\x18! \x01(\x08:\x05\x66\x61lse\x12_\n\x11idempotency_level\x18\" \x01(\x0e\x32/.google.protobuf.MethodOptions.IdempotencyLevel:\x13IDEMPOTENCY_UNKNOWN\x12-\n\x08\x66\x65\x61tures\x18# \x01(\x0b\x32\x1b.google.protobuf.FeatureSet\x12\x43\n\x14uninterpreted_option\x18\xe7\x07 \x03(\x0b\x32$.google.protobuf.UninterpretedOption\"P\n\x10IdempotencyLevel\x12\x17\n\x13IDEMPOTENCY_UNKNOWN\x10\x00\x12\x13\n\x0fNO_SIDE_EFFECTS\x10\x01\x12\x0e\n\nIDEMPOTENT\x10\x02*\t\x08\xe8\x07\x10\x80\x80\x80\x80\x02\"\x9e\x02\n\x13UninterpretedOption\x12;\n\x04name\x18\x02 \x03(\x0b\x32-.google.protobuf.UninterpretedOption.NamePart\x12\x18\n\x10identifier_value\x18\x03 \x01(\t\x12\x1a\n\x12positive_int_value\x18\x04 \x01(\x04\x12\x1a\n\x12negative_int_value\x18\x05 \x01(\x03\x12\x14\n\x0c\x64ouble_value\x18\x06 \x01(\x01\x12\x14\n\x0cstring_value\x18\x07 \x01(\x0c\x12\x17\n\x0f\x61ggregate_value\x18\x08 \x01(\t\x1a\x33\n\x08NamePart\x12\x11\n\tname_part\x18\x01 \x02(\t\x12\x14\n\x0cis_extension\x18\x02 \x02(\x08\"\xaa\t\n\nFeatureSet\x12_\n\x0e\x66ield_presence\x18\x01 \x01(\x0e\x32).google.protobuf.FeatureSet.FieldPresenceB\x1c\x88\x01\x01\x98\x01\x04\x98\x01\x01\xa2\x01\x10\x12\x08\x45XPLICIT\n\x04\x32\x30\x32\x33\x12Q\n\tenum_type\x18\x02 \x01(\x0e\x32$.google.protobuf.FeatureSet.EnumTypeB\x18\x88\x01\x01\x98\x01\x06\x98\x01\x01\xa2\x01\x0c\x12\x04OPEN\n\x04\x32\x30\x32\x33\x12n\n\x17repeated_field_encoding\x18\x03 \x01(\x0e\x32\x31.google.protobuf.FeatureSet.RepeatedFieldEncodingB\x1a\x88\x01\x01\x98\x01\x04\x98\x01\x01\xa2\x01\x0e\x12\x06PACKED\n\x04\x32\x30\x32\x33\x12q\n\x17string_field_validation\x18\x04 \x01(\x0e\x32\x31.google.protobuf.FeatureSet.StringFieldValidationB\x1d\x88\x01\x01\x98\x01\x04\x98\x01\x01\xa2\x01\x11\x12\tMANDATORY\n\x04\x32\x30\x32\x33\x12j\n\x10message_encoding\x18\x05 \x01(\x0e\x32+.google.protobuf.FeatureSet.MessageEncodingB#\x88\x01\x01\x98\x01\x04\x98\x01\x01\xa2\x01\x17\x12\x0fLENGTH_PREFIXED\n\x04\x32\x30\x32\x33\x12Y\n\x0bjson_format\x18\x06 \x01(\x0e\x32&.google.protobuf.FeatureSet.JsonFormatB\x1c\x88\x01\x01\x98\x01\x03\x98\x01\x06\x98\x01\x01\xa2\x01\r\x12\x05\x41LLOW\n\x04\x32\x30\x32\x33\x12\x37\n\x0craw_features\x18\xe7\x07 \x01(\x0b\x32\x1b.google.protobuf.FeatureSetB\x03\x98\x01\x00\"\\\n\rFieldPresence\x12\x1a\n\x16\x46IELD_PRESENCE_UNKNOWN\x10\x00\x12\x0c\n\x08\x45XPLICIT\x10\x01\x12\x0c\n\x08IMPLICIT\x10\x02\x12\x13\n\x0fLEGACY_REQUIRED\x10\x03\"7\n\x08\x45numType\x12\x15\n\x11\x45NUM_TYPE_UNKNOWN\x10\x00\x12\x08\n\x04OPEN\x10\x01\x12\n\n\x06\x43LOSED\x10\x02\"V\n\x15RepeatedFieldEncoding\x12#\n\x1fREPEATED_FIELD_ENCODING_UNKNOWN\x10\x00\x12\n\n\x06PACKED\x10\x01\x12\x0c\n\x08\x45XPANDED\x10\x02\"_\n\x15StringFieldValidation\x12#\n\x1fSTRING_FIELD_VALIDATION_UNKNOWN\x10\x00\x12\r\n\tMANDATORY\x10\x01\x12\x08\n\x04HINT\x10\x02\x12\x08\n\x04NONE\x10\x03\"S\n\x0fMessageEncoding\x12\x1c\n\x18MESSAGE_ENCODING_UNKNOWN\x10\x00\x12\x13\n\x0fLENGTH_PREFIXED\x10\x01\x12\r\n\tDELIMITED\x10\x02\"H\n\nJsonFormat\x12\x17\n\x13JSON_FORMAT_UNKNOWN\x10\x00\x12\t\n\x05\x41LLOW\x10\x01\x12\x16\n\x12LEGACY_BEST_EFFORT\x10\x02*\x06\x08\xe8\x07\x10\xe9\x07*\x06\x08\xe9\x07\x10\xea\x07*\x06\x08\x8bN\x10\x90N\"\xd5\x01\n\x0eSourceCodeInfo\x12:\n\x08location\x18\x01 \x03(\x0b\x32(.google.protobuf.SourceCodeInfo.Location\x1a\x86\x01\n\x08Location\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x10\n\x04span\x18\x02 \x03(\x05\x42\x02\x10\x01\x12\x18\n\x10leading_comments\x18\x03 \x01(\t\x12\x19\n\x11trailing_comments\x18\x04 \x01(\t\x12!\n\x19leading_detached_comments\x18\x06 \x03(\t\"\x9c\x02\n\x11GeneratedCodeInfo\x12\x41\n\nannotation\x18\x01 \x03(\x0b\x32-.google.protobuf.GeneratedCodeInfo.Annotation\x1a\xc3\x01\n\nAnnotation\x12\x10\n\x04path\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x13\n\x0bsource_file\x18\x02 \x01(\t\x12\r\n\x05\x62\x65gin\x18\x03 \x01(\x05\x12\x0b\n\x03\x65nd\x18\x04 \x01(\x05\x12H\n\x08semantic\x18\x05 \x01(\x0e\x32\x36.google.protobuf.GeneratedCodeInfo.Annotation.Semantic\"(\n\x08Semantic\x12\x08\n\x04NONE\x10\x00\x12\x07\n\x03SET\x10\x01\x12\t\n\x05\x41LIAS\x10\x02\x42~\n\x13\x63om.google.protobufB\x10\x44\x65scriptorProtosH\x01Z-google.golang.org/protobuf/types/descriptorpb\xf8\x01\x01\xa2\x02\x03GPB\xaa\x02\x1aGoogle.Protobuf.Reflection')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'descriptor_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.google.protobufB\020DescriptorProtosH\001Z-google.golang.org/protobuf/types/descriptorpb\370\001\001\242\002\003GPB\252\002\032Google.Protobuf.Reflection'
  _globals['_EXTENSIONRANGEOPTIONS'].fields_by_name['declaration']._loaded_options = None
  _globals['_EXTENSIONRANGEOPTIONS'].fields_by_name['declaration']._serialized_options = b'\210\001\002'
  _globals['_FILEOPTIONS'].fields_by_name['java_generate_equals_and_hash']._loaded_options = None
  _globals['_FILEOPTIONS'].fields_by_name['java_generate_equals_and_hash']._serialized_options = b'\030\001'
  _globals['_MESSAGEOPTIONS'].fields_by_name['deprecated_legacy_json_field_conflicts']._loaded_options = None
  _globals['_MESSAGEOPTIONS'].fields_by_name['deprecated_legacy_json_field_conflicts']._serialized_options = b'\030\001'
  _globals['_ENUMOPTIONS'].fields_by_name['deprecated_legacy_json_field_conflicts']._loaded_options = None
  _globals['_ENUMOPTIONS'].fields_by_name['deprecated_legacy_json_field_conflicts']._serialized_options = b'\030\001'
  _globals['_FEATURESET'].fields_by_name['field_presence']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['field_presence']._serialized_options = b'\210\001\001\230\001\004\230\001\001\242\001\020\022\010EXPLICIT\n\0042023'
  _globals['_FEATURESET'].fields_by_name['enum_type']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['enum_type']._serialized_options = b'\210\001\001\230\001\006\230\001\001\242\001\014\022\004OPEN\n\0042023'
  _globals['_FEATURESET'].fields_by_name['repeated_field_encoding']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['repeated_field_encoding']._serialized_options = b'\210\001\001\230\001\004\230\001\001\242\001\016\022\006PACKED\n\0042023'
  _globals['_FEATURESET'].fields_by_name['string_field_validation']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['string_field_validation']._serialized_options = b'\210\001\001\230\001\004\230\001\001\242\001\021\022\tMANDATORY\n\0042023'
  _globals['_FEATURESET'].fields_by_name['message_encoding']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['message_encoding']._serialized_options = b'\210\001\001\230\001\004\230\001\001\242\001\027\022\017LENGTH_PREFIXED\n\0042023'
  _globals['_FEATURESET'].fields_by_name['json_format']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['json_format']._serialized_options = b'\210\001\001\230\001\003\230\001\006\230\001\001\242\001\r\022\005ALLOW\n\0042023'
  _globals['_FEATURESET'].fields_by_name['raw_features']._loaded_options = None
  _globals['_FEATURESET'].fields_by_name['raw_features']._serialized_options = b'\230\001\000'
  _globals['_SOURCECODEINFO_LOCATION'].fields_by_name['path']._loaded_options = None
  _globals['_SOURCECODEINFO_LOCATION'].fields_by_name['path']._serialized_options = b'\020\001'
  _globals['_SOURCECODEINFO_LOCATION'].fields_by_name['span']._loaded_options = None
  _globals['_SOURCECODEINFO_LOCATION'].fields_by_name['span']._serialized_options = b'\020\001'
  _globals['_GENERATEDCODEINFO_ANNOTATION'].fields_by_name['path']._loaded_options = None
  _globals['_GENERATEDCODEINFO_ANNOTATION'].fields_by_name['path']._serialized_options = b'\020\001'
  _globals['_FILEDESCRIPTORSET']._serialized_start=37
  _globals['_FILEDESCRIPTORSET']._serialized_end=108
  _globals['_FILEDESCRIPTORPROTO']._serialized_start=111
  _globals['_FILEDESCRIPTORPROTO']._serialized_end=603
  _globals['_DESCRIPTORPROTO']._serialized_start=606
  _globals['_DESCRIPTORPROTO']._serialized_end=1287
  _globals['_DESCRIPTORPROTO_EXTENSIONRANGE']._serialized_start=1141
  _globals['_DESCRIPTORPROTO_EXTENSIONRANGE']._serialized_end=1242
  _globals['_DESCRIPTORPROTO_RESERVEDRANGE']._serialized_start=1244
  _globals['_DESCRIPTORPROTO_RESERVEDRANGE']._serialized_end=1287
  _globals['_EXTENSIONRANGEOPTIONS']._serialized_start=1290
  _globals['_EXTENSIONRANGEOPTIONS']._serialized_end=1770
  _globals['_EXTENSIONRANGEOPTIONS_DECLARATION']._serialized_start=1601
  _globals['_EXTENSIONRANGEOPTIONS_DECLARATION']._serialized_end=1705
  _globals['_EXTENSIONRANGEOPTIONS_VERIFICATIONSTATE']._serialized_start=1707
  _globals['_EXTENSIONRANGEOPTIONS_VERIFICATIONSTATE']._serialized_end=1759
  _globals['_FIELDDESCRIPTORPROTO']._serialized_start=1773
  _globals['_FIELDDESCRIPTORPROTO']._serialized_end=2498
  _globals['_FIELDDESCRIPTORPROTO_TYPE']._serialized_start=2119
  _globals['_FIELDDESCRIPTORPROTO_TYPE']._serialized_end=2429
  _globals['_FIELDDESCRIPTORPROTO_LABEL']._serialized_start=2431
  _globals['_FIELDDESCRIPTORPROTO_LABEL']._serialized_end=2498
  _globals['_ONEOFDESCRIPTORPROTO']._serialized_start=2500
  _globals['_ONEOFDESCRIPTORPROTO']._serialized_end=2584
  _globals['_ENUMDESCRIPTORPROTO']._serialized_start=2587
  _globals['_ENUMDESCRIPTORPROTO']._serialized_end=2879
  _globals['_ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE']._serialized_start=2832
  _globals['_ENUMDESCRIPTORPROTO_ENUMRESERVEDRANGE']._serialized_end=2879
  _globals['_ENUMVALUEDESCRIPTORPROTO']._serialized_start=2881
  _globals['_ENUMVALUEDESCRIPTORPROTO']._serialized_end=2989
  _globals['_SERVICEDESCRIPTORPROTO']._serialized_start=2992
  _globals['_SERVICEDESCRIPTORPROTO']._serialized_end=3136
  _globals['_METHODDESCRIPTORPROTO']._serialized_start=3139
  _globals['_METHODDESCRIPTORPROTO']._serialized_end=3332
  _globals['_FILEOPTIONS']._serialized_start=3335
  _globals['_FILEOPTIONS']._serialized_end=4187
  _globals['_FILEOPTIONS_OPTIMIZEMODE']._serialized_start=4112
  _globals['_FILEOPTIONS_OPTIMIZEMODE']._serialized_end=4170
  _globals['_MESSAGEOPTIONS']._serialized_start=4190
  _globals['_MESSAGEOPTIONS']._serialized_end=4549
  _globals['_FIELDOPTIONS']._serialized_start=4552
  _globals['_FIELDOPTIONS']._serialized_end=5691
  _globals['_FIELDOPTIONS_EDITIONDEFAULT']._serialized_start=5158
  _globals['_FIELDOPTIONS_EDITIONDEFAULT']._serialized_end=5206
  _globals['_FIELDOPTIONS_CTYPE']._serialized_start=5208
  _globals['_FIELDOPTIONS_CTYPE']._serialized_end=5255
  _globals['_FIELDOPTIONS_JSTYPE']._serialized_start=5257
  _globals['_FIELDOPTIONS_JSTYPE']._serialized_end=5310
  _globals['_FIELDOPTIONS_OPTIONRETENTION']._serialized_start=5312
  _globals['_FIELDOPTIONS_OPTIONRETENTION']._serialized_end=5397
  _globals['_FIELDOPTIONS_OPTIONTARGETTYPE']._serialized_start=5400
  _globals['_FIELDOPTIONS_OPTIONTARGETTYPE']._serialized_end=5668
  _globals['_ONEOFOPTIONS']._serialized_start=5694
  _globals['_ONEOFOPTIONS']._serialized_end=5835
  _globals['_ENUMOPTIONS']._serialized_start=5838
  _globals['_ENUMOPTIONS']._serialized_end=6084
  _globals['_ENUMVALUEOPTIONS']._serialized_start=6087
  _globals['_ENUMVALUEOPTIONS']._serialized_end=6288
  _globals['_SERVICEOPTIONS']._serialized_start=6291
  _globals['_SERVICEOPTIONS']._serialized_end=6461
  _globals['_METHODOPTIONS']._serialized_start=6464
  _globals['_METHODOPTIONS']._serialized_end=6812
  _globals['_METHODOPTIONS_IDEMPOTENCYLEVEL']._serialized_start=6721
  _globals['_METHODOPTIONS_IDEMPOTENCYLEVEL']._serialized_end=6801
  _globals['_UNINTERPRETEDOPTION']._serialized_start=6815
  _globals['_UNINTERPRETEDOPTION']._serialized_end=7101
  _globals['_UNINTERPRETEDOPTION_NAMEPART']._serialized_start=7050
  _globals['_UNINTERPRETEDOPTION_NAMEPART']._serialized_end=7101
  _globals['_FEATURESET']._serialized_start=7104
  _globals['_FEATURESET']._serialized_end=8298
  _globals['_FEATURESET_FIELDPRESENCE']._serialized_start=7781
  _globals['_FEATURESET_FIELDPRESENCE']._serialized_end=7873
  _globals['_FEATURESET_ENUMTYPE']._serialized_start=7875
  _globals['_FEATURESET_ENUMTYPE']._serialized_end=7930
  _globals['_FEATURESET_REPEATEDFIELDENCODING']._serialized_start=7932
  _globals['_FEATURESET_REPEATEDFIELDENCODING']._serialized_end=8018
  _globals['_FEATURESET_STRINGFIELDVALIDATION']._serialized_start=8020
  _globals['_FEATURESET_STRINGFIELDVALIDATION']._serialized_end=8115
  _globals['_FEATURESET_MESSAGEENCODING']._serialized_start=8117
  _globals['_FEATURESET_MESSAGEENCODING']._serialized_end=8200
  _globals['_FEATURESET_JSONFORMAT']._serialized_start=8202
  _globals['_FEATURESET_JSONFORMAT']._serialized_end=8274
  _globals['_SOURCECODEINFO']._serialized_start=8301
  _globals['_SOURCECODEINFO']._serialized_end=8514
  _globals['_SOURCECODEINFO_LOCATION']._serialized_start=8380
  _globals['_SOURCECODEINFO_LOCATION']._serialized_end=8514
  _globals['_GENERATEDCODEINFO']._serialized_start=8517
  _globals['_GENERATEDCODEINFO']._serialized_end=8801
  _globals['_GENERATEDCODEINFO_ANNOTATION']._serialized_start=8606
  _globals['_GENERATEDCODEINFO_ANNOTATION']._serialized_end=8801
  _globals['_GENERATEDCODEINFO_ANNOTATION_SEMANTIC']._serialized_start=8761
  _globals['_GENERATEDCODEINFO_ANNOTATION_SEMANTIC']._serialized_end=8801
# @@protoc_insertion_point(module_scope)
