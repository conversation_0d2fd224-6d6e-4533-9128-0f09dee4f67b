# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: client-actions.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'client-actions.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import client_workspaces_pb2 as client__workspaces__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x63lient-actions.proto\x12,com.augmentcode.sidecar.rpc.clientInterfaces\x1a\x17\x63lient-workspaces.proto\"\xd9\x01\n\x13ShowDiffViewRequest\x12M\n\x04path\x18\x01 \x01(\x0b\x32?.com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName\x12\x10\n\x08original\x18\x02 \x01(\t\x12\x10\n\x08modified\x18\x03 \x01(\t\x12O\n\x04opts\x18\x04 \x01(\x0b\x32\x41.com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewOptions\"?\n\x13ShowDiffViewOptions\x12\x18\n\x0bretainFocus\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\x0e\n\x0c_retainFocusBG\n,com.augmentcode.sidecar.rpc.clientInterfacesB\x15\x43lientActionsRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'client_actions_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n,com.augmentcode.sidecar.rpc.clientInterfacesB\025ClientActionsRPCTypesP\001'
  _globals['_SHOWDIFFVIEWREQUEST']._serialized_start=96
  _globals['_SHOWDIFFVIEWREQUEST']._serialized_end=313
  _globals['_SHOWDIFFVIEWOPTIONS']._serialized_start=315
  _globals['_SHOWDIFFVIEWOPTIONS']._serialized_end=378
# @@protoc_insertion_point(module_scope)
