# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import chat_messages_pb2 as chat__messages__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
import intellij_chat_pb2 as intellij__chat__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in intellij_chat_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WebviewChatServiceStub(object):
    """
    This service defines an abstraction of an interface that the web views can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ChatLoaded = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatLoaded',
                request_serializer=intellij__chat__pb2.ChatLoadedRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatInitializeResponse.FromString,
                _registered_method=True)
        self.ChatUserMessage = channel.unary_stream(
                '/com.augmentcode.rpc.WebviewChatService/ChatUserMessage',
                request_serializer=intellij__chat__pb2.ChatUserMessageRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatModelReply.FromString,
                _registered_method=True)
        self.ChatUserCancel = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatUserCancel',
                request_serializer=intellij__chat__pb2.ChatUserCancelRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.FindFolder = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindFolder',
                request_serializer=intellij__chat__pb2.FindFolderRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindFolderResponse.FromString,
                _registered_method=True)
        self.FindFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindFile',
                request_serializer=intellij__chat__pb2.FindFileRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindFileResponse.FromString,
                _registered_method=True)
        self.ResolveFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ResolveFile',
                request_serializer=intellij__chat__pb2.ResolveFileRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ResolveFileResponse.FromString,
                _registered_method=True)
        self.OpenFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/OpenFile',
                request_serializer=intellij__chat__pb2.OpenFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.FindRecentlyOpenedFiles = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindRecentlyOpenedFiles',
                request_serializer=intellij__chat__pb2.FindRecentlyOpenedFilesRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindRecentlyOpenedFilesResponse.FromString,
                _registered_method=True)
        self.ChatSmartPaste = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatSmartPaste',
                request_serializer=intellij__chat__pb2.ChatSmartPasteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ChatCreateFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatCreateFile',
                request_serializer=intellij__chat__pb2.ChatCreateFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ChatFeedback = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatFeedback',
                request_serializer=intellij__chat__pb2.ChatRatingMessage.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatRatingDoneMessage.FromString,
                _registered_method=True)
        self.GetUserConfirmation = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/GetUserConfirmation',
                request_serializer=intellij__chat__pb2.OpenConfirmationModal.SerializeToString,
                response_deserializer=intellij__chat__pb2.ConfirmationModalResponse.FromString,
                _registered_method=True)
        self.FindExternalSources = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindExternalSources',
                request_serializer=intellij__chat__pb2.FindExternalSourcesRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindExternalSourcesResponse.FromString,
                _registered_method=True)
        self.FindSymbol = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindSymbol',
                request_serializer=intellij__chat__pb2.FindSymbolRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindSymbolResponse.FromString,
                _registered_method=True)
        self.FindSymbolRegex = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/FindSymbolRegex',
                request_serializer=intellij__chat__pb2.FindSymbolRegexRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.FindSymbolResponse.FromString,
                _registered_method=True)
        self.SaveChat = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/SaveChat',
                request_serializer=chat__messages__pb2.SaveChatRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.SaveChatDoneResponse.FromString,
                _registered_method=True)
        self.GetDiagnostics = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/GetDiagnostics',
                request_serializer=intellij__chat__pb2.GetDiagnosticsRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.GetDiagnosticsResponse.FromString,
                _registered_method=True)
        self.OpenMemoriesFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/OpenMemoriesFile',
                request_serializer=intellij__chat__pb2.OpenMemoriesFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.OpenSettingsPage = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/OpenSettingsPage',
                request_serializer=intellij__chat__pb2.OpenSettingsPageRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.UpdateGuidelinesState = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/UpdateGuidelinesState',
                request_serializer=intellij__chat__pb2.UpdateGuidelinesStateRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ChatSaveImage = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatSaveImage',
                request_serializer=intellij__chat__pb2.ChatSaveImageRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatSaveImageResponse.FromString,
                _registered_method=True)
        self.ChatLoadImage = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatLoadImage',
                request_serializer=intellij__chat__pb2.ChatLoadImageRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatLoadImageResponse.FromString,
                _registered_method=True)
        self.ChatDeleteImage = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewChatService/ChatDeleteImage',
                request_serializer=intellij__chat__pb2.ChatDeleteImageRequest.SerializeToString,
                response_deserializer=intellij__chat__pb2.ChatDeleteImageResponse.FromString,
                _registered_method=True)


class WebviewChatServiceServicer(object):
    """
    This service defines an abstraction of an interface that the web views can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def ChatLoaded(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatUserMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatUserCancel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindFolder(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResolveFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindRecentlyOpenedFiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatSmartPaste(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatCreateFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatFeedback(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUserConfirmation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindExternalSources(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindSymbol(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FindSymbolRegex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveChat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDiagnostics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenMemoriesFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenSettingsPage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateGuidelinesState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatSaveImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatLoadImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChatDeleteImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WebviewChatServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ChatLoaded': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatLoaded,
                    request_deserializer=intellij__chat__pb2.ChatLoadedRequest.FromString,
                    response_serializer=intellij__chat__pb2.ChatInitializeResponse.SerializeToString,
            ),
            'ChatUserMessage': grpc.unary_stream_rpc_method_handler(
                    servicer.ChatUserMessage,
                    request_deserializer=intellij__chat__pb2.ChatUserMessageRequest.FromString,
                    response_serializer=intellij__chat__pb2.ChatModelReply.SerializeToString,
            ),
            'ChatUserCancel': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatUserCancel,
                    request_deserializer=intellij__chat__pb2.ChatUserCancelRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FindFolder': grpc.unary_unary_rpc_method_handler(
                    servicer.FindFolder,
                    request_deserializer=intellij__chat__pb2.FindFolderRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindFolderResponse.SerializeToString,
            ),
            'FindFile': grpc.unary_unary_rpc_method_handler(
                    servicer.FindFile,
                    request_deserializer=intellij__chat__pb2.FindFileRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindFileResponse.SerializeToString,
            ),
            'ResolveFile': grpc.unary_unary_rpc_method_handler(
                    servicer.ResolveFile,
                    request_deserializer=intellij__chat__pb2.ResolveFileRequest.FromString,
                    response_serializer=intellij__chat__pb2.ResolveFileResponse.SerializeToString,
            ),
            'OpenFile': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenFile,
                    request_deserializer=intellij__chat__pb2.OpenFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FindRecentlyOpenedFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.FindRecentlyOpenedFiles,
                    request_deserializer=intellij__chat__pb2.FindRecentlyOpenedFilesRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindRecentlyOpenedFilesResponse.SerializeToString,
            ),
            'ChatSmartPaste': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatSmartPaste,
                    request_deserializer=intellij__chat__pb2.ChatSmartPasteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ChatCreateFile': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatCreateFile,
                    request_deserializer=intellij__chat__pb2.ChatCreateFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ChatFeedback': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatFeedback,
                    request_deserializer=intellij__chat__pb2.ChatRatingMessage.FromString,
                    response_serializer=intellij__chat__pb2.ChatRatingDoneMessage.SerializeToString,
            ),
            'GetUserConfirmation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserConfirmation,
                    request_deserializer=intellij__chat__pb2.OpenConfirmationModal.FromString,
                    response_serializer=intellij__chat__pb2.ConfirmationModalResponse.SerializeToString,
            ),
            'FindExternalSources': grpc.unary_unary_rpc_method_handler(
                    servicer.FindExternalSources,
                    request_deserializer=intellij__chat__pb2.FindExternalSourcesRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindExternalSourcesResponse.SerializeToString,
            ),
            'FindSymbol': grpc.unary_unary_rpc_method_handler(
                    servicer.FindSymbol,
                    request_deserializer=intellij__chat__pb2.FindSymbolRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindSymbolResponse.SerializeToString,
            ),
            'FindSymbolRegex': grpc.unary_unary_rpc_method_handler(
                    servicer.FindSymbolRegex,
                    request_deserializer=intellij__chat__pb2.FindSymbolRegexRequest.FromString,
                    response_serializer=intellij__chat__pb2.FindSymbolResponse.SerializeToString,
            ),
            'SaveChat': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveChat,
                    request_deserializer=chat__messages__pb2.SaveChatRequest.FromString,
                    response_serializer=intellij__chat__pb2.SaveChatDoneResponse.SerializeToString,
            ),
            'GetDiagnostics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDiagnostics,
                    request_deserializer=intellij__chat__pb2.GetDiagnosticsRequest.FromString,
                    response_serializer=intellij__chat__pb2.GetDiagnosticsResponse.SerializeToString,
            ),
            'OpenMemoriesFile': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenMemoriesFile,
                    request_deserializer=intellij__chat__pb2.OpenMemoriesFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'OpenSettingsPage': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenSettingsPage,
                    request_deserializer=intellij__chat__pb2.OpenSettingsPageRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'UpdateGuidelinesState': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateGuidelinesState,
                    request_deserializer=intellij__chat__pb2.UpdateGuidelinesStateRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ChatSaveImage': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatSaveImage,
                    request_deserializer=intellij__chat__pb2.ChatSaveImageRequest.FromString,
                    response_serializer=intellij__chat__pb2.ChatSaveImageResponse.SerializeToString,
            ),
            'ChatLoadImage': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatLoadImage,
                    request_deserializer=intellij__chat__pb2.ChatLoadImageRequest.FromString,
                    response_serializer=intellij__chat__pb2.ChatLoadImageResponse.SerializeToString,
            ),
            'ChatDeleteImage': grpc.unary_unary_rpc_method_handler(
                    servicer.ChatDeleteImage,
                    request_deserializer=intellij__chat__pb2.ChatDeleteImageRequest.FromString,
                    response_serializer=intellij__chat__pb2.ChatDeleteImageResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.augmentcode.rpc.WebviewChatService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.augmentcode.rpc.WebviewChatService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WebviewChatService(object):
    """
    This service defines an abstraction of an interface that the web views can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    @staticmethod
    def ChatLoaded(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatLoaded',
            intellij__chat__pb2.ChatLoadedRequest.SerializeToString,
            intellij__chat__pb2.ChatInitializeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatUserMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatUserMessage',
            intellij__chat__pb2.ChatUserMessageRequest.SerializeToString,
            intellij__chat__pb2.ChatModelReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatUserCancel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatUserCancel',
            intellij__chat__pb2.ChatUserCancelRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindFolder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindFolder',
            intellij__chat__pb2.FindFolderRequest.SerializeToString,
            intellij__chat__pb2.FindFolderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindFile',
            intellij__chat__pb2.FindFileRequest.SerializeToString,
            intellij__chat__pb2.FindFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResolveFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ResolveFile',
            intellij__chat__pb2.ResolveFileRequest.SerializeToString,
            intellij__chat__pb2.ResolveFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/OpenFile',
            intellij__chat__pb2.OpenFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindRecentlyOpenedFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindRecentlyOpenedFiles',
            intellij__chat__pb2.FindRecentlyOpenedFilesRequest.SerializeToString,
            intellij__chat__pb2.FindRecentlyOpenedFilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatSmartPaste(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatSmartPaste',
            intellij__chat__pb2.ChatSmartPasteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatCreateFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatCreateFile',
            intellij__chat__pb2.ChatCreateFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatFeedback(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatFeedback',
            intellij__chat__pb2.ChatRatingMessage.SerializeToString,
            intellij__chat__pb2.ChatRatingDoneMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetUserConfirmation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/GetUserConfirmation',
            intellij__chat__pb2.OpenConfirmationModal.SerializeToString,
            intellij__chat__pb2.ConfirmationModalResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindExternalSources(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindExternalSources',
            intellij__chat__pb2.FindExternalSourcesRequest.SerializeToString,
            intellij__chat__pb2.FindExternalSourcesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindSymbol(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindSymbol',
            intellij__chat__pb2.FindSymbolRequest.SerializeToString,
            intellij__chat__pb2.FindSymbolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FindSymbolRegex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/FindSymbolRegex',
            intellij__chat__pb2.FindSymbolRegexRequest.SerializeToString,
            intellij__chat__pb2.FindSymbolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveChat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/SaveChat',
            chat__messages__pb2.SaveChatRequest.SerializeToString,
            intellij__chat__pb2.SaveChatDoneResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDiagnostics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/GetDiagnostics',
            intellij__chat__pb2.GetDiagnosticsRequest.SerializeToString,
            intellij__chat__pb2.GetDiagnosticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenMemoriesFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/OpenMemoriesFile',
            intellij__chat__pb2.OpenMemoriesFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenSettingsPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/OpenSettingsPage',
            intellij__chat__pb2.OpenSettingsPageRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateGuidelinesState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/UpdateGuidelinesState',
            intellij__chat__pb2.UpdateGuidelinesStateRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatSaveImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatSaveImage',
            intellij__chat__pb2.ChatSaveImageRequest.SerializeToString,
            intellij__chat__pb2.ChatSaveImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatLoadImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatLoadImage',
            intellij__chat__pb2.ChatLoadImageRequest.SerializeToString,
            intellij__chat__pb2.ChatLoadImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChatDeleteImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewChatService/ChatDeleteImage',
            intellij__chat__pb2.ChatDeleteImageRequest.SerializeToString,
            intellij__chat__pb2.ChatDeleteImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
