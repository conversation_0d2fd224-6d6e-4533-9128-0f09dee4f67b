# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: augment.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'augment.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\raugment.proto\x12\x13\x63om.augmentcode.rpc\x1a\x19google/protobuf/any.proto\x1a google/protobuf/descriptor.proto\"\xa1\x01\n\x0c\x41syncWrapper\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\r\n\x05\x65rror\x18\x02 \x01(\t\x12%\n\x07\x62\x61seMsg\x18\x03 \x01(\x0b\x32\x14.google.protobuf.Any\x12\x35\n\tstreamCtx\x18\x04 \x01(\x0b\x32\".com.augmentcode.rpc.StreamContext:\x11\xba\xe8t\rasync-wrapper\"\\\n\rStreamContext\x12\x14\n\x0cstreamMsgIdx\x18\x01 \x01(\x05\x12\x1b\n\x13streamNextRequestId\x18\x02 \x01(\t\x12\x18\n\x10isStreamComplete\x18\x03 \x01(\x08:?\n\x14webview_message_type\x12\x1f.google.protobuf.MessageOptions\x18\x87\xcd\x0e \x01(\tB%\n\x13\x63om.augmentcode.rpcB\x0c\x41ugmentTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'augment_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.augmentcode.rpcB\014AugmentTypesP\001'
  _globals['_ASYNCWRAPPER']._loaded_options = None
  _globals['_ASYNCWRAPPER']._serialized_options = b'\272\350t\rasync-wrapper'
  _globals['_ASYNCWRAPPER']._serialized_start=100
  _globals['_ASYNCWRAPPER']._serialized_end=261
  _globals['_STREAMCONTEXT']._serialized_start=263
  _globals['_STREAMCONTEXT']._serialized_end=355
# @@protoc_insertion_point(module_scope)
