# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: intellij-preferences.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'intellij-preferences.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
import augment_pb2 as augment__pb2
import intellij_chat_pb2 as intellij__chat__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1aintellij-preferences.proto\x12\x13\x63om.augmentcode.rpc\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/descriptor.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\raugment.proto\x1a\x13intellij-chat.proto\"*\n\x17PreferenceNotifyRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\"7\n\x18PreferencesLoadedRequest:\x1b\xba\xe8t\x17preference-panel-loaded\"h\n\x1dPreferencesInitializeResponse\x12\x32\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32$.com.augmentcode.rpc.PreferenceInput:\x13\xba\xe8t\x0fpreference-init\"|\n\x0fPreferenceInput\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x31\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32#.com.augmentcode.rpc.PreferencePair\x12(\n enable_retrieval_data_collection\x18\x03 \x01(\x08\"t\n\x0ePreferencePair\x12\x30\n\x01\x61\x18\x01 \x01(\x0b\x32%.com.augmentcode.rpc.AugmentChatEntry\x12\x30\n\x01\x62\x18\x02 \x01(\x0b\x32%.com.augmentcode.rpc.AugmentChatEntry\"5\n\x10\x41ugmentChatEntry\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x10\n\x08response\x18\x02 \x01(\t\"m\n\x17PreferenceResultRequest\x12\x33\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32%.com.augmentcode.rpc.PreferenceResult:\x1d\xba\xe8t\x19preference-result-message\"\xb9\x01\n\x10PreferenceResult\x12\x16\n\x0eoverall_rating\x18\x01 \x01(\t\x12\x19\n\x11\x66ormatting_rating\x18\x02 \x01(\t\x12\x1c\n\x14hallucination_rating\x18\x03 \x01(\t\x12$\n\x1cinstruction_following_rating\x18\x04 \x01(\t\x12\x15\n\rtext_feedback\x18\x05 \x01(\t\x12\x17\n\x0fis_high_quality\x18\x06 \x01(\x08\"-\n\x15\x43hatStreamDoneMessage:\x14\xba\xe8t\x10\x63hat-stream-done2\xcb\x02\n\x19WebviewPreferencesService\x12X\n\x10PreferenceNotify\x12,.com.augmentcode.rpc.PreferenceNotifyRequest\x1a\x16.google.protobuf.Empty\x12x\n\x11PreferencesLoaded\x12-.com.augmentcode.rpc.PreferencesLoadedRequest\x1a\x32.com.augmentcode.rpc.PreferencesInitializeResponse\"\x00\x12Z\n\x10PreferenceResult\x12,.com.augmentcode.rpc.PreferenceResultRequest\x1a\x16.google.protobuf.Empty\"\x00\x42)\n\x13\x63om.augmentcode.rpcB\x10PreferencesTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'intellij_preferences_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.augmentcode.rpcB\020PreferencesTypesP\001'
  _globals['_PREFERENCESLOADEDREQUEST']._loaded_options = None
  _globals['_PREFERENCESLOADEDREQUEST']._serialized_options = b'\272\350t\027preference-panel-loaded'
  _globals['_PREFERENCESINITIALIZERESPONSE']._loaded_options = None
  _globals['_PREFERENCESINITIALIZERESPONSE']._serialized_options = b'\272\350t\017preference-init'
  _globals['_PREFERENCERESULTREQUEST']._loaded_options = None
  _globals['_PREFERENCERESULTREQUEST']._serialized_options = b'\272\350t\031preference-result-message'
  _globals['_CHATSTREAMDONEMESSAGE']._loaded_options = None
  _globals['_CHATSTREAMDONEMESSAGE']._serialized_options = b'\272\350t\020chat-stream-done'
  _globals['_PREFERENCENOTIFYREQUEST']._serialized_start=180
  _globals['_PREFERENCENOTIFYREQUEST']._serialized_end=222
  _globals['_PREFERENCESLOADEDREQUEST']._serialized_start=224
  _globals['_PREFERENCESLOADEDREQUEST']._serialized_end=279
  _globals['_PREFERENCESINITIALIZERESPONSE']._serialized_start=281
  _globals['_PREFERENCESINITIALIZERESPONSE']._serialized_end=385
  _globals['_PREFERENCEINPUT']._serialized_start=387
  _globals['_PREFERENCEINPUT']._serialized_end=511
  _globals['_PREFERENCEPAIR']._serialized_start=513
  _globals['_PREFERENCEPAIR']._serialized_end=629
  _globals['_AUGMENTCHATENTRY']._serialized_start=631
  _globals['_AUGMENTCHATENTRY']._serialized_end=684
  _globals['_PREFERENCERESULTREQUEST']._serialized_start=686
  _globals['_PREFERENCERESULTREQUEST']._serialized_end=795
  _globals['_PREFERENCERESULT']._serialized_start=798
  _globals['_PREFERENCERESULT']._serialized_end=983
  _globals['_CHATSTREAMDONEMESSAGE']._serialized_start=985
  _globals['_CHATSTREAMDONEMESSAGE']._serialized_end=1030
  _globals['_WEBVIEWPREFERENCESSERVICE']._serialized_start=1033
  _globals['_WEBVIEWPREFERENCESSERVICE']._serialized_end=1364
# @@protoc_insertion_point(module_scope)
