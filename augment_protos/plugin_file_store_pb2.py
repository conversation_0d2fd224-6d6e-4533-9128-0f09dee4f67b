# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: plugin-file-store.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'plugin-file-store.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17plugin-file-store.proto\x12,com.augmentcode.sidecar.rpc.clientInterfaces\"+\n\tSaveAsset\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x10\n\x08\x63ontents\x18\x02 \x01(\x0c\"\x19\n\tLoadAsset\x12\x0c\n\x04path\x18\x01 \x01(\t\"%\n\x11LoadAssetResponse\x12\x10\n\x08\x63ontents\x18\x01 \x01(\x0c\"\x1b\n\x0b\x44\x65leteAsset\x12\x0c\n\x04path\x18\x01 \x01(\t\"\x1c\n\nListAssets\x12\x0e\n\x06prefix\x18\x01 \x01(\t\"#\n\x12ListAssetsResponse\x12\r\n\x05paths\x18\x01 \x03(\tBI\n,com.augmentcode.sidecar.rpc.clientInterfacesB\x17PluginFileStoreRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'plugin_file_store_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n,com.augmentcode.sidecar.rpc.clientInterfacesB\027PluginFileStoreRPCTypesP\001'
  _globals['_SAVEASSET']._serialized_start=73
  _globals['_SAVEASSET']._serialized_end=116
  _globals['_LOADASSET']._serialized_start=118
  _globals['_LOADASSET']._serialized_end=143
  _globals['_LOADASSETRESPONSE']._serialized_start=145
  _globals['_LOADASSETRESPONSE']._serialized_end=182
  _globals['_DELETEASSET']._serialized_start=184
  _globals['_DELETEASSET']._serialized_end=211
  _globals['_LISTASSETS']._serialized_start=213
  _globals['_LISTASSETS']._serialized_end=241
  _globals['_LISTASSETSRESPONSE']._serialized_start=243
  _globals['_LISTASSETSRESPONSE']._serialized_end=278
# @@protoc_insertion_point(module_scope)
