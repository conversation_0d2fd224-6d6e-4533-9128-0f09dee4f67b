# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: intellij-chat.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'intellij-chat.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
import augment_pb2 as augment__pb2
import chat_pb2 as chat__pb2
import chat_messages_pb2 as chat__messages__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13intellij-chat.proto\x12\x13\x63om.augmentcode.rpc\x1a\x1bgoogle/protobuf/empty.proto\x1a google/protobuf/descriptor.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\raugment.proto\x1a\nchat.proto\x1a\x13\x63hat_messages.proto\"g\n\x14SaveChatDoneResponse\x12;\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32-.com.augmentcode.rpc.SaveChatDoneResponseData:\x12\xba\xe8t\x0esave-chat-done\"5\n\x18SaveChatDoneResponseData\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"$\n\x11\x43hatLoadedRequest:\x0f\xba\xe8t\x0b\x63hat-loaded\"l\n\x16\x43hatInitializeResponse\x12=\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32/.com.augmentcode.rpc.ChatInitializeResponseData:\x13\xba\xe8t\x0f\x63hat-initialize\"\xcb\x08\n\x1a\x43hatInitializeResponseData\x12\"\n\x1a\x65nablePreferenceCollection\x18\x01 \x01(\x08\x12\x1b\n\x13\x65nableDebugFeatures\x18\x02 \x01(\x08\x12\x1a\n\x12useRichTextHistory\x18\x03 \x01(\x08\x12\x1c\n\x14smartPastePromptPath\x18\x04 \x01(\t\x12g\n\x14modelDisplayNameToId\x18\x05 \x03(\x0b\x32I.com.augmentcode.rpc.ChatInitializeResponseData.ModelDisplayNameToIdEntry\x12\x14\n\x0c\x66ullFeatured\x18\x06 \x01(\x08\x12\x16\n\x0e\x65nableFlywheel\x18\x07 \x01(\x08\x12#\n\x1b\x65nableExternalSourcesInChat\x18\x08 \x01(\x08\x12\x1a\n\x12\x65nableShareService\x18\t \x01(\x08\x12\x19\n\x11useNewThreadsMenu\x18\n \x01(\x08\x12\x18\n\x10\x65nableSmartPaste\x18\x0b \x01(\x08\x12 \n\x18smartPastePrecomputeMode\x18\x0c \x01(\t\x12(\n enableDesignSystemRichTextEditor\x18\r \x01(\x08\x12!\n\x19\x65nableChatMermaidDiagrams\x18\x0e \x01(\x08\x12\x17\n\x0f\x65nableAgentMode\x18\x0f \x01(\x08\x12\x10\n\x08userTier\x18\x10 \x01(\t\x12\x1d\n\x15\x65nableEditableHistory\x18\x11 \x01(\x08\x12\x1c\n\x14\x65nableChatMultimodal\x18\x13 \x01(\x08\x12\x1c\n\x14\x65nablePromptEnhancer\x18\x14 \x01(\x08\x12\x1b\n\x13\x65nableAgentAutoMode\x18\x15 \x01(\x08\x12\x1c\n\x14\x65nableNewThreadsList\x18\x16 \x01(\x08\x12\x16\n\x0e\x65nableTaskList\x18\x17 \x01(\x08\x12-\n%conversationHistorySizeThresholdBytes\x18\x18 \x01(\x05\x12\x19\n\x11useHistorySummary\x18\x19 \x01(\x08\x12\x1e\n\x16historySummaryMaxChars\x18\x1a \x01(\x05\x12 \n\x18historySummaryLowerChars\x18\x1b \x01(\x05\x12\x1c\n\x14historySummaryPrompt\x18\x1c \x01(\t\x12\x13\n\x0b\x65nableRules\x18\x1d \x01(\x08\x12\x1d\n\x15\x65nableExchangeStorage\x18\x1e \x01(\x08\x12\x1f\n\x17retryChatStreamTimeouts\x18\x1f \x01(\x08\x12!\n\x19\x65nableToolUseStateStorage\x18  \x01(\x08\x1a;\n\x19ModelDisplayNameToIdEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"g\n\x16\x43hatUserMessageRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.ChatUserMessageData:\x15\xba\xe8t\x11\x63hat-user-message\"\xe5\x04\n\x13\x43hatUserMessageData\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x46\n\x0b\x63hatHistory\x18\x02 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\x43\n\x12userSpecifiedFiles\x18\x03 \x03(\x0b\x32\'.com.augmentcode.rpc.IQualifiedPathName\x12\x19\n\x11\x65xternalSourceIds\x18\x04 \x03(\t\x12@\n\x05nodes\x18\x05 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatRequestNode\x12\x14\n\x07modelId\x18\x06 \x01(\tH\x00\x88\x01\x01\x12=\n\x07\x63ontext\x18\x07 \x01(\x0b\x32\'.com.augmentcode.rpc.IChatActiveContextH\x01\x88\x01\x01\x12\x1d\n\x10\x64isableRetrieval\x18\x08 \x01(\x08H\x02\x88\x01\x01\x12\'\n\x1a\x64isableSelectedCodeDetails\x18\t \x01(\x08H\x03\x88\x01\x01\x12<\n\x0cmemoriesInfo\x18\n \x01(\x0b\x32!.com.augmentcode.rpc.MemoriesInfoH\x04\x88\x01\x01\x12\x13\n\x06silent\x18\x0b \x01(\x08H\x05\x88\x01\x01\x42\n\n\x08_modelIdB\n\n\x08_contextB\x13\n\x11_disableRetrievalB\x1d\n\x1b_disableSelectedCodeDetailsB\x0f\n\r_memoriesInfoB\t\n\x07_silent\"\xf9\x02\n\x12IChatActiveContext\x12\x43\n\x12userSpecifiedFiles\x18\x01 \x03(\x0b\x32\'.com.augmentcode.rpc.IQualifiedPathName\x12<\n\x0brecentFiles\x18\x02 \x03(\x0b\x32\'.com.augmentcode.rpc.IQualifiedPathName\x12<\n\x0f\x65xternalSources\x18\x03 \x03(\x0b\x32#.com.augmentcode.rpc.ExternalSource\x12\x34\n\nselections\x18\x04 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails\x12>\n\rsourceFolders\x18\x05 \x03(\x0b\x32\'.com.augmentcode.rpc.IQualifiedPathName\x12,\n\truleFiles\x18\x06 \x03(\x0b\x32\x19.com.augmentcode.rpc.Rule\"g\n\x04Rule\x12+\n\x04type\x18\x01 \x01(\x0e\x32\x1d.com.augmentcode.rpc.RuleType\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\"\"\n\x0f\x43hatRequestText\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"\\\n\x10\x43hatRequestImage\x12\x12\n\nimage_data\x18\x01 \x01(\t\x12\x34\n\x06\x66ormat\x18\x02 \x01(\x0e\x32$.com.augmentcode.rpc.ImageFormatType\"\xd8\x01\n\x16\x43hatRequestContentNode\x12=\n\x04type\x18\x01 \x01(\x0e\x32/.com.augmentcode.rpc.ChatRequestContentNodeType\x12\x19\n\x0ctext_content\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x41\n\rimage_content\x18\x03 \x01(\x0b\x32%.com.augmentcode.rpc.ChatRequestImageH\x01\x88\x01\x01\x42\x0f\n\r_text_contentB\x10\n\x0e_image_content\"\x80\x02\n\x15\x43hatRequestToolResult\x12\x13\n\x0btool_use_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x10\n\x08is_error\x18\x03 \x01(\x08\x12\x17\n\nrequest_id\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x39\n\x05image\x18\x05 \x01(\x0b\x32%.com.augmentcode.rpc.ChatRequestImageH\x01\x88\x01\x01\x12\x42\n\rcontent_nodes\x18\x06 \x03(\x0b\x32+.com.augmentcode.rpc.ChatRequestContentNodeB\r\n\x0b_request_idB\x08\n\x06_image\"7\n\x12IQualifiedPathName\x12\x10\n\x08rootPath\x18\x01 \x01(\t\x12\x0f\n\x07relPath\x18\x02 \x01(\t\"}\n\x0e\x43hatModelReply\x12\x35\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\'.com.augmentcode.rpc.ChatModelReplyData\x12\x13\n\x06stream\x18\x02 \x01(\tH\x00\x88\x01\x01:\x14\xba\xe8t\x10\x63hat-model-replyB\t\n\x07_stream\"\x8a\x02\n\x12\x43hatModelReplyData\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x11\n\trequestId\x18\x02 \x01(\t\x12\x11\n\tstreaming\x18\x03 \x01(\x08\x12\x44\n\x13workspaceFileChunks\x18\x04 \x03(\x0b\x32\'.com.augmentcode.rpc.WorkspaceFileChunk\x12\x32\n\x05nodes\x18\x05 \x03(\x0b\x32#.com.augmentcode.rpc.ChatResultNode\x12<\n\x05\x65rror\x18\x06 \x01(\x0b\x32(.com.augmentcode.rpc.ChatModelReplyErrorH\x00\x88\x01\x01\x42\x08\n\x06_error\"\\\n\x13\x43hatModelReplyError\x12\x1b\n\x13\x64isplayErrorMessage\x18\x01 \x01(\t\x12\x18\n\x0bisRetriable\x18\x02 \x01(\x08H\x00\x88\x01\x01\x42\x0e\n\x0c_isRetriable\"\xf3\x01\n\nChatResult\x12\x0c\n\x04text\x18\x01 \x01(\t\x12[\n\x13workspaceFileChunks\x18\x02 \x03(\x0b\x32\'.com.augmentcode.rpc.WorkspaceFileChunkR\x15workspace_file_chunks\x12\x32\n\x05nodes\x18\x03 \x03(\x0b\x32#.com.augmentcode.rpc.ChatResultNode\x12<\n\x05\x65rror\x18\x04 \x01(\x0b\x32(.com.augmentcode.rpc.ChatModelReplyErrorH\x00\x88\x01\x01\x42\x08\n\x06_error\"\xa1\x01\n\x12WorkspaceFileChunk\x12\x1d\n\tcharStart\x18\x01 \x01(\x03R\nchar_start\x12\x19\n\x07\x63harEnd\x18\x02 \x01(\x03R\x08\x63har_end\x12\x1b\n\x08\x62lobName\x18\x03 \x01(\tR\tblob_name\x12\x34\n\x04\x66ile\x18\x04 \x01(\x0b\x32 .com.augmentcode.rpc.FileDetailsR\x04\x66ile\"\x9c\x02\n\x0e\x43hatResultNode\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x35\n\x04type\x18\x02 \x01(\x0e\x32\'.com.augmentcode.rpc.ChatResultNodeType\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x46\n\x07toolUse\x18\x04 \x01(\x0b\x32&.com.augmentcode.rpc.ChatResultToolUseH\x00R\x08tool_use\x88\x01\x01\x12R\n\x0b\x61gentMemory\x18\x05 \x01(\x0b\x32*.com.augmentcode.rpc.ChatResultAgentMemoryH\x01R\x0c\x61gent_memory\x88\x01\x01\x42\n\n\x08_toolUseB\x0e\n\x0c_agentMemory\"(\n\x15\x43hatResultAgentMemory\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"\xbb\x01\n\x11\x43hatResultToolUse\x12\x1e\n\ttoolUseId\x18\x01 \x01(\tR\x0btool_use_id\x12\x1b\n\x08toolName\x18\x02 \x01(\tR\ttool_name\x12\x1d\n\tinputJson\x18\x03 \x01(\tR\ninput_json\x12&\n\rmcpServerName\x18\x04 \x01(\tR\x0fmcp_server_name\x12\"\n\x0bmcpToolName\x18\x05 \x01(\tR\rmcp_tool_name\"d\n\x15\x43hatUserCancelRequest\x12\x35\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\'.com.augmentcode.rpc.ChatUserCancelData:\x14\xba\xe8t\x10\x63hat-user-cancel\"\'\n\x12\x43hatUserCancelData\x12\x11\n\trequestId\x18\x01 \x01(\t\"o\n\x14SourceFoldersUpdated\x12;\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32-.com.augmentcode.rpc.SourceFoldersUpdatedData:\x1a\xba\xe8t\x16source-folders-updated\"Y\n\x18SourceFoldersUpdatedData\x12=\n\rsourceFolders\x18\x01 \x03(\x0b\x32&.com.augmentcode.rpc.ISourceFolderInfo\"\'\n\x11ISourceFolderInfo\x12\x12\n\nfolderRoot\x18\x01 \x01(\t\"^\n\x12\x46ileRangesSelected\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails:\x18\xba\xe8t\x14\x66ile-ranges-selected\"^\n\x12\x43urrentlyOpenFiles\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails:\x18\xba\xe8t\x14\x63urrently-open-files\"\xf5\x03\n\x0b\x46ileDetails\x12\x10\n\x08repoRoot\x18\x01 \x01(\t\x12\x10\n\x08pathName\x18\x02 \x01(\t\x12.\n\x05range\x18\x03 \x01(\x0b\x32\x1a.com.augmentcode.rpc.RangeH\x00\x88\x01\x01\x12\x36\n\tfullRange\x18\x04 \x01(\x0b\x32\x1e.com.augmentcode.rpc.FullRangeH\x01\x88\x01\x01\x12\x19\n\x0coriginalCode\x18\x05 \x01(\tH\x02\x88\x01\x01\x12\x19\n\x0cmodifiedCode\x18\x06 \x01(\tH\x03\x88\x01\x01\x12:\n\x0blineChanges\x18\x07 \x01(\x0b\x32 .com.augmentcode.rpc.LineChangesH\x04\x88\x01\x01\x12\x19\n\x0c\x64ifferentTab\x18\x08 \x01(\x08H\x05\x88\x01\x01\x12\x16\n\trequestId\x18\t \x01(\tH\x06\x88\x01\x01\x12\x19\n\x0csuggestionId\x18\n \x01(\tH\x07\x88\x01\x01\x12\x14\n\x07snippet\x18\x0b \x01(\tH\x08\x88\x01\x01\x42\x08\n\x06_rangeB\x0c\n\n_fullRangeB\x0f\n\r_originalCodeB\x0f\n\r_modifiedCodeB\x0e\n\x0c_lineChangesB\x0f\n\r_differentTabB\x0c\n\n_requestIdB\x0f\n\r_suggestionIdB\n\n\x08_snippet\"$\n\x05Range\x12\r\n\x05start\x18\x01 \x01(\x05\x12\x0c\n\x04stop\x18\x02 \x01(\x05\"c\n\tFullRange\x12\x17\n\x0fstartLineNumber\x18\x01 \x01(\x05\x12\x13\n\x0bstartColumn\x18\x02 \x01(\x05\x12\x15\n\rendLineNumber\x18\x03 \x01(\x05\x12\x11\n\tendColumn\x18\x04 \x01(\x05\"S\n\x0bLineChanges\x12\x30\n\x07\x63hanges\x18\x01 \x03(\x0b\x32\x1f.com.augmentcode.rpc.LineChange\x12\x12\n\nlineOffset\x18\x02 \x01(\x05\"d\n\nLineChange\x12\x15\n\roriginalStart\x18\x01 \x01(\x05\x12\x13\n\x0boriginalEnd\x18\x02 \x01(\x05\x12\x15\n\rmodifiedStart\x18\x03 \x01(\x05\x12\x13\n\x0bmodifiedEnd\x18\x04 \x01(\x05\"`\n\x0f\x46indFileRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.FindFileRequestData:\x15\xba\xe8t\x11\x66ind-file-request\"`\n\x13\x46indFileRequestData\x12\x10\n\x08rootPath\x18\x01 \x01(\t\x12\x0f\n\x07relPath\x18\x02 \x01(\t\x12\x12\n\nexactMatch\x18\x03 \x01(\x08\x12\x12\n\nmaxResults\x18\x04 \x01(\x05\"f\n\x12ResolveFileRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.FindFileRequestData:\x18\xba\xe8t\x14resolve-file-request\"`\n\x13ResolveFileResponse\x12.\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32 .com.augmentcode.rpc.FileDetails:\x19\xba\xe8t\x15resolve-file-response\"Z\n\x10\x46indFileResponse\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails:\x16\xba\xe8t\x12\x66ind-file-response\"p\n\x17SourceFoldersSyncStatus\x12\x35\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\'.com.augmentcode.rpc.SyncingStatusEvent:\x1e\xba\xe8t\x1asource-folders-sync-status\"\x93\x01\n\x12SyncingStatusEvent\x12\x32\n\x06status\x18\x01 \x01(\x0e\x32\".com.augmentcode.rpc.SyncingStatus\x12I\n\x0f\x66oldersProgress\x18\x02 \x03(\x0b\x32\x30.com.augmentcode.rpc.SourceFolderSyncingProgress\"i\n\x1bSourceFolderSyncingProgress\x12\x12\n\nfolderRoot\x18\x01 \x01(\t\x12\x36\n\x08progress\x18\x02 \x01(\x0b\x32$.com.augmentcode.rpc.SyncingProgress\"R\n\x0fSyncingProgress\x12\x14\n\x0cnewlyTracked\x18\x01 \x01(\x08\x12\x14\n\x0ctrackedFiles\x18\x02 \x01(\x05\x12\x13\n\x0b\x62\x61\x63klogSize\x18\x03 \x01(\x05\"P\n\x0fOpenFileRequest\x12.\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32 .com.augmentcode.rpc.FileDetails:\r\xba\xe8t\topen-file\"k\n\x15\x43hatCreateFileRequest\x12<\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32..com.augmentcode.rpc.ChatCreateFileRequestData:\x14\xba\xe8t\x10\x63hat-create-file\":\n\x19\x43hatCreateFileRequestData\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0f\n\x07relPath\x18\x02 \x01(\t\"\x91\x01\n\x0c\x43hatFeedback\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x33\n\x06rating\x18\x02 \x01(\x0e\x32#.com.augmentcode.rpc.FeedbackRating\x12\x0c\n\x04note\x18\x03 \x01(\t\x12+\n\x04mode\x18\x04 \x01(\x0e\x32\x1d.com.augmentcode.rpc.ChatMode\"U\n\x11\x43hatRatingMessage\x12/\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32!.com.augmentcode.rpc.ChatFeedback:\x0f\xba\xe8t\x0b\x63hat-rating\"^\n\x15\x43hatRatingDoneMessage\x12/\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32!.com.augmentcode.rpc.ChatFeedback:\x14\xba\xe8t\x10\x63hat-rating-done\"\xa0\x01\n ReportWebviewClientMetricRequest\x12\x14\n\x0c\x63lientMetric\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03\x12\x35\n\x0bwebviewName\x18\x03 \x01(\x0e\x32 .com.augmentcode.rpc.WebviewName: \xba\xe8t\x1creport-webview-client-metric\"r\n\x15OpenConfirmationModal\x12<\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32..com.augmentcode.rpc.OpenConfirmationModalData:\x1b\xba\xe8t\x17open-confirmation-modal\"p\n\x19OpenConfirmationModalData\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x19\n\x11\x63onfirmButtonText\x18\x03 \x01(\t\x12\x18\n\x10\x63\x61ncelButtonText\x18\x04 \x01(\t\"~\n\x19\x43onfirmationModalResponse\x12@\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x32.com.augmentcode.rpc.ConfirmationModalResponseData:\x1f\xba\xe8t\x1b\x63onfirmation-modal-response\":\n\x1d\x43onfirmationModalResponseData\x12\n\n\x02ok\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"C\n\x1cSearchExternalSourcesRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x14\n\x0csource_types\x18\x02 \x03(\t\"U\n\x1dSearchExternalSourcesResponse\x12\x34\n\x07sources\x18\x01 \x03(\x0b\x32#.com.augmentcode.rpc.ExternalSource\"\x80\x01\n\x1a\x46indExternalSourcesRequest\x12?\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x31.com.augmentcode.rpc.SearchExternalSourcesRequest:!\xba\xe8t\x1d\x66ind-external-sources-request\"\x83\x01\n\x1b\x46indExternalSourcesResponse\x12@\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x32.com.augmentcode.rpc.SearchExternalSourcesResponse:\"\xba\xe8t\x1e\x66ind-external-sources-response\"N\n\x0e\x45xternalSource\x12\n\n\x02id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0bsource_type\x18\x04 \x01(\t\"f\n\x11\x46indSymbolRequest\x12\x38\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32*.com.augmentcode.rpc.FindSymbolRequestData:\x17\xba\xe8t\x13\x66ind-symbol-request\"b\n\x15\x46indSymbolRequestData\x12\r\n\x05query\x18\x01 \x01(\t\x12:\n\x0bsearchScope\x18\x02 \x01(\x0b\x32%.com.augmentcode.rpc.ISearchScopeArgs\"C\n\x10ISearchScopeArgs\x12/\n\x05\x66iles\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails\"q\n\x16\x46indSymbolRegexRequest\x12\x38\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32*.com.augmentcode.rpc.FindSymbolRequestData:\x1d\xba\xe8t\x19\x66ind-symbol-regex-request\"H\n\x16\x46indSymbolResponseData\x12.\n\x04\x66ile\x18\x01 \x01(\x0b\x32 .com.augmentcode.rpc.FileDetails\"i\n\x12\x46indSymbolResponse\x12\x39\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32+.com.augmentcode.rpc.FindSymbolResponseData:\x18\xba\xe8t\x14\x66ind-symbol-response\"f\n\x11\x46indFolderRequest\x12\x38\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32*.com.augmentcode.rpc.FindFolderRequestData:\x17\xba\xe8t\x13\x66ind-folder-request\"b\n\x15\x46indFolderRequestData\x12\x10\n\x08rootPath\x18\x01 \x01(\t\x12\x0f\n\x07relPath\x18\x02 \x01(\t\x12\x12\n\nexactMatch\x18\x03 \x01(\x08\x12\x12\n\nmaxResults\x18\x04 \x01(\x05\"^\n\x12\x46indFolderResponse\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails:\x18\xba\xe8t\x14\x66ind-folder-response\"x\n\x1e\x46indRecentlyOpenedFilesRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.FindFileRequestData:\x1e\xba\xe8t\x1a\x66ind-recently-opened-files\"z\n\x1f\x46indRecentlyOpenedFilesResponse\x12.\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32 .com.augmentcode.rpc.FileDetails:\'\xba\xe8t#find-recently-opened-files-response\"(\n\x0fMainPanelLoaded:\x15\xba\xe8t\x11main-panel-loaded\"?\n\x13MainPanelDisplayApp\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1a\xba\xe8t\x16main-panel-display-app\"8\n\x10MainPanelActions\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\t:\x16\xba\xe8t\x12main-panel-actions\"E\n\x16MainPanelPerformAction\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1d\xba\xe8t\x19main-panel-perform-action\"d\n\x15\x43hatSmartPasteRequest\x12\x35\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\'.com.augmentcode.rpc.ChatSmartPasteData:\x14\xba\xe8t\x10\x63hat-smart-paste\"\xc4\x01\n\x12\x43hatSmartPasteData\x12\x15\n\rgeneratedCode\x18\x01 \x01(\t\x12\x46\n\x0b\x63hatHistory\x18\x02 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\x12\n\ntargetFile\x18\x04 \x01(\t\x12;\n\x07options\x18\x03 \x01(\x0b\x32*.com.augmentcode.rpc.ChatSmartPasteOptions\"Y\n\x15\x43hatSmartPasteOptions\x12\x0e\n\x06\x64ryRun\x18\x01 \x01(\x08\x12\x0f\n\x07preload\x18\x02 \x01(\x08\x12\x1f\n\x17requireFileConfirmation\x18\x03 \x01(\x08\"\x19\n\x08UsedChat:\r\xba\xe8t\tused-chat\"(\n\x0fUsedSlashAction:\x15\xba\xe8t\x11used-slash-action\",\n\x11ShouldShowSummary:\x17\xba\xe8t\x13should-show-summary\"4\n\x15GetDiagnosticsRequest:\x1b\xba\xe8t\x17get-diagnostics-request\"e\n\x16GetDiagnosticsResponse\x12-\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1f.com.augmentcode.rpc.Diagnostic:\x1c\xba\xe8t\x18get-diagnostics-response\"\xb2\x01\n\nDiagnostic\x12=\n\x08location\x18\x01 \x01(\x0b\x32+.com.augmentcode.rpc.DiagnosticFileLocation\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x39\n\x08severity\x18\x03 \x01(\x0e\x32\'.com.augmentcode.rpc.DiagnosticSeverity\x12\x19\n\x11\x63urrent_blob_name\x18\x04 \x01(\t\"L\n\x16\x44iagnosticFileLocation\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x12\n\nline_start\x18\x02 \x01(\x05\x12\x10\n\x08line_end\x18\x03 \x01(\x05\"\x8e\x03\n\x1b\x43hatInstructionStreamResult\x12\x12\n\x04text\x18\x01 \x01(\tR\x04text\x12,\n\x10unknownBlobNames\x18\x02 \x03(\tR\x12unknown_blob_names\x12\x30\n\x12\x63heckpointNotFound\x18\x03 \x01(\x08R\x14\x63heckpoint_not_found\x12)\n\x0freplacementText\x18\x04 \x01(\tR\x10replacement_text\x12\x30\n\x12replacementOldText\x18\x05 \x01(\tR\x14replacement_old_text\x12\x34\n\x14replacementStartLine\x18\x06 \x01(\x05R\x16replacement_start_line\x12\x30\n\x12replacementEndLine\x18\x07 \x01(\x05R\x14replacement_end_line\x12\x36\n\x15replacementSequenceId\x18\x08 \x01(\x05R\x17replacement_sequence_id\"c\n\x1bToolCheckSafeWebViewRequest\x12\x34\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32&.com.augmentcode.rpc.ToolCheckSafeData:\x0e\xba\xe8t\ncheck-safe\"I\n\x11ToolCheckSafeData\x12\x0c\n\x04name\x18\x01 \x01(\t\x12&\n\x05input\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"u\n\x1cToolCheckSafeWebViewResponse\x12<\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32..com.augmentcode.rpc.ToolCheckSafeResponseData:\x17\xba\xe8t\x13\x63heck-safe-response\"+\n\x19ToolCheckSafeResponseData\x12\x0e\n\x06isSafe\x18\x01 \x01(\x08\"_\n\x16ToolCallWebViewRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.ToolCallRequestData:\r\xba\xe8t\tcall-tool\"\xd1\x01\n\x13ToolCallRequestData\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x11\n\ttoolUseId\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12&\n\x05input\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x46\n\x0b\x63hatHistory\x18\x05 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\x16\n\x0e\x63onversationId\x18\x06 \x01(\t\"q\n\x17ToolCallWebViewResponse\x12>\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x30.com.augmentcode.rpc.ToolCallWebViewResponseData:\x16\xba\xe8t\x12\x63\x61ll-tool-response\"b\n\x1bToolCallWebViewResponseData\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0f\n\x07isError\x18\x02 \x01(\x08\x12\x16\n\trequestId\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x0c\n\n_requestId\"h\n\x1bToolCancelRunWebViewRequest\x12\x34\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32&.com.augmentcode.rpc.ToolCancelRunData:\x13\xba\xe8t\x0f\x63\x61ncel-tool-run\"9\n\x11ToolCancelRunData\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x11\n\ttoolUseId\x18\x02 \x01(\t\"<\n\x1cToolCancelRunWebViewResponse:\x1c\xba\xe8t\x18\x63\x61ncel-tool-run-response\"?\n\x16\x43heckToolExistsRequest\x12\x10\n\x08toolName\x18\x01 \x01(\t:\x13\xba\xe8t\x0f\x63heckToolExists\"F\n\x17\x43heckToolExistsResponse\x12\x0e\n\x06\x65xists\x18\x01 \x01(\x08:\x1b\xba\xe8t\x17\x63heckToolExistsResponse\"n\n\x1d\x43hatModeChangedWebViewRequest\x12\x36\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.ChatModeChangedData:\x15\xba\xe8t\x11\x63hat-mode-changed\"#\n\x13\x43hatModeChangedData\x12\x0c\n\x04mode\x18\x01 \x01(\t\"G\n!CheckAgentAutoModeApprovalRequest:\"\xba\xe8t\x1e\x63heck-agent-auto-mode-approval\"_\n\"CheckAgentAutoModeApprovalResponse\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x08:+\xba\xe8t\'check-agent-auto-mode-approval-response\"Q\n\x1fSetAgentAutoModeApprovedRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x08: \xba\xe8t\x1cset-agent-auto-mode-approved\"8\n\x16GetIDEStateNodeRequest:\x1e\xba\xe8t\x1aget-ide-state-node-request\"\x7f\n\x17GetIDEStateNodeResponse\x12\x43\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x35.com.augmentcode.sidecar.rpc.chat.ChatRequestIdeState:\x1f\xba\xe8t\x1bget-ide-state-node-response\"1\n\x17OpenMemoriesFileRequest:\x16\xba\xe8t\x12open-memories-file\"9\n\x17GetWorkspaceInfoRequest:\x1e\xba\xe8t\x1aget-workspace-info-request\"|\n\x18GetWorkspaceInfoResponse\x12?\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x31.com.augmentcode.rpc.GetWorkspaceInfoResponseData:\x1f\xba\xe8t\x1bget-workspace-info-response\"8\n\x1cGetWorkspaceInfoResponseData\x12\x18\n\x10trackedFileCount\x18\x01 \x03(\x05\":\n\x13UserGuidelinesState\x12\x11\n\toverLimit\x18\x01 \x01(\x08\x12\x10\n\x08\x63ontents\x18\x02 \x01(\t\"[\n\x18WorkspaceGuidelinesState\x12\x11\n\toverLimit\x18\x01 \x01(\x08\x12\x17\n\x0fworkspaceFolder\x18\x02 \x01(\t\x12\x13\n\x0blengthLimit\x18\x03 \x01(\x05\"\xa0\x01\n\x10GuidelinesStates\x12@\n\x0euserGuidelines\x18\x01 \x01(\x0b\x32(.com.augmentcode.rpc.UserGuidelinesState\x12J\n\x13workspaceGuidelines\x18\x02 \x03(\x0b\x32-.com.augmentcode.rpc.WorkspaceGuidelinesState\"p\n\x1cUpdateGuidelinesStateRequest\x12\x33\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32%.com.augmentcode.rpc.GuidelinesStates:\x1b\xba\xe8t\x17update-guidelines-state\"?\n\x17OpenSettingsPageRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x16\xba\xe8t\x12open-settings-page\"*\n\x08\x46ileData\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\"`\n\x14\x43hatSaveImageRequest\x12+\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x1d.com.augmentcode.rpc.FileData:\x1b\xba\xe8t\x17\x63hat-save-image-request\"C\n\x15\x43hatSaveImageResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1c\xba\xe8t\x18\x63hat-save-image-response\"A\n\x14\x43hatLoadImageRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1b\xba\xe8t\x17\x63hat-load-image-request\"C\n\x15\x43hatLoadImageResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1c\xba\xe8t\x18\x63hat-load-image-response\"E\n\x16\x43hatDeleteImageRequest\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x1d\xba\xe8t\x19\x63hat-delete-image-request\"9\n\x17\x43hatDeleteImageResponse:\x1e\xba\xe8t\x1a\x63hat-delete-image-response\"\"\n\x0cSignInLoaded:\x12\xba\xe8t\x0esign-in-loaded\"p\n\x14SignInLoadedResponse\x12;\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32-.com.augmentcode.rpc.SignInLoadedResponseData:\x1b\xba\xe8t\x17sign-in-loaded-response\"*\n\x18SignInLoadedResponseData\x12\x0e\n\x06\x63lient\x18\x01 \x01(\t\"4\n\x12\x41ugmentLinkMessage\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t:\x10\xba\xe8t\x0c\x61ugment-link\"?\n\x0cMemoriesInfo\x12\x1c\n\x14isClassifyAndDistill\x18\x01 \x01(\x08\x12\x11\n\tisDistill\x18\x02 \x01(\x08\"7\n\x1aGetSubscriptionInfoRequest:\x19\xba\xe8t\x15get-subscription-info\"\x85\x01\n\x1bGetSubscriptionInfoResponse\x12\x42\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x34.com.augmentcode.rpc.GetSubscriptionInfoResponseData:\"\xba\xe8t\x1eget-subscription-info-response\"\xee\x01\n\x1fGetSubscriptionInfoResponseData\x12?\n\nenterprise\x18\x01 \x01(\x0b\x32+.com.augmentcode.rpc.SubscriptionEnterprise\x12\x41\n\x12\x61\x63tiveSubscription\x18\x02 \x01(\x0b\x32%.com.augmentcode.rpc.SubscriptionInfo\x12G\n\x14inactiveSubscription\x18\x03 \x01(\x0b\x32).com.augmentcode.rpc.InactiveSubscription\"\x18\n\x16SubscriptionEnterprise\"\x16\n\x14InactiveSubscription\"R\n\x10SubscriptionInfo\x12\x14\n\x07\x65ndDate\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x14usageBalanceDepleted\x18\x02 \x01(\x08\x42\n\n\x08_endDate*@\n\x08RuleType\x12\x13\n\x0f\x41LWAYS_ATTACHED\x10\x00\x12\n\n\x06MANUAL\x10\x01\x12\x13\n\x0f\x41GENT_REQUESTED\x10\x02*_\n\x1a\x43hatRequestContentNodeType\x12\x1c\n\x18\x43ONTENT_TYPE_UNSPECIFIED\x10\x00\x12\x10\n\x0c\x43ONTENT_TEXT\x10\x01\x12\x11\n\rCONTENT_IMAGE\x10\x02*U\n\x0fImageFormatType\x12\x1c\n\x18IMAGE_FORMAT_UNSPECIFIED\x10\x00\x12\x07\n\x03PNG\x10\x01\x12\x08\n\x04JPEG\x10\x02\x12\x07\n\x03GIF\x10\x03\x12\x08\n\x04WEBP\x10\x04*\xb8\x01\n\x12\x43hatResultNodeType\x12\x10\n\x0cRAW_RESPONSE\x10\x00\x12\x17\n\x13SUGGESTED_QUESTIONS\x10\x01\x12\x16\n\x12MAIN_TEXT_FINISHED\x10\x02\x12\x19\n\x15WORKSPACE_FILE_CHUNKS\x10\x03\x12\x14\n\x10RELEVANT_SOURCES\x10\x04\x12\x0c\n\x08TOOL_USE\x10\x05\x12\x12\n\x0eTOOL_USE_START\x10\x07\x12\x0c\n\x08THINKING\x10\x08*7\n\rSyncingStatus\x12\x0f\n\x0blongRunning\x10\x00\x12\x0b\n\x07running\x10\x01\x12\x08\n\x04\x64one\x10\x02*7\n\x0e\x46\x65\x65\x64\x62\x61\x63kRating\x12\t\n\x05unset\x10\x00\x12\x0c\n\x08positive\x10\x01\x12\x0c\n\x08negative\x10\x02*\x1f\n\x08\x43hatMode\x12\x08\n\x04\x43HAT\x10\x00\x12\t\n\x05\x41GENT\x10\x01*\x17\n\x0bWebviewName\x12\x08\n\x04\x63hat\x10\x00*G\n\x12\x44iagnosticSeverity\x12\t\n\x05\x45RROR\x10\x00\x12\x0b\n\x07WARNING\x10\x01\x12\x0f\n\x0bINFORMATION\x10\x02\x12\x08\n\x04HINT\x10\x03\x32\xc3\x12\n\x12WebviewChatService\x12\x63\n\nChatLoaded\x12&.com.augmentcode.rpc.ChatLoadedRequest\x1a+.com.augmentcode.rpc.ChatInitializeResponse\"\x00\x12g\n\x0f\x43hatUserMessage\x12+.com.augmentcode.rpc.ChatUserMessageRequest\x1a#.com.augmentcode.rpc.ChatModelReply\"\x00\x30\x01\x12V\n\x0e\x43hatUserCancel\x12*.com.augmentcode.rpc.ChatUserCancelRequest\x1a\x16.google.protobuf.Empty\"\x00\x12_\n\nFindFolder\x12&.com.augmentcode.rpc.FindFolderRequest\x1a\'.com.augmentcode.rpc.FindFolderResponse\"\x00\x12Y\n\x08\x46indFile\x12$.com.augmentcode.rpc.FindFileRequest\x1a%.com.augmentcode.rpc.FindFileResponse\"\x00\x12\x62\n\x0bResolveFile\x12\'.com.augmentcode.rpc.ResolveFileRequest\x1a(.com.augmentcode.rpc.ResolveFileResponse\"\x00\x12J\n\x08OpenFile\x12$.com.augmentcode.rpc.OpenFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\x86\x01\n\x17\x46indRecentlyOpenedFiles\x12\x33.com.augmentcode.rpc.FindRecentlyOpenedFilesRequest\x1a\x34.com.augmentcode.rpc.FindRecentlyOpenedFilesResponse\"\x00\x12V\n\x0e\x43hatSmartPaste\x12*.com.augmentcode.rpc.ChatSmartPasteRequest\x1a\x16.google.protobuf.Empty\"\x00\x12V\n\x0e\x43hatCreateFile\x12*.com.augmentcode.rpc.ChatCreateFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\x64\n\x0c\x43hatFeedback\x12&.com.augmentcode.rpc.ChatRatingMessage\x1a*.com.augmentcode.rpc.ChatRatingDoneMessage\"\x00\x12s\n\x13GetUserConfirmation\x12*.com.augmentcode.rpc.OpenConfirmationModal\x1a..com.augmentcode.rpc.ConfirmationModalResponse\"\x00\x12z\n\x13\x46indExternalSources\x12/.com.augmentcode.rpc.FindExternalSourcesRequest\x1a\x30.com.augmentcode.rpc.FindExternalSourcesResponse\"\x00\x12_\n\nFindSymbol\x12&.com.augmentcode.rpc.FindSymbolRequest\x1a\'.com.augmentcode.rpc.FindSymbolResponse\"\x00\x12i\n\x0f\x46indSymbolRegex\x12+.com.augmentcode.rpc.FindSymbolRegexRequest\x1a\'.com.augmentcode.rpc.FindSymbolResponse\"\x00\x12p\n\x08SaveChat\x12\x37.com.augmentcode.common.webviews.protos.SaveChatRequest\x1a).com.augmentcode.rpc.SaveChatDoneResponse\"\x00\x12k\n\x0eGetDiagnostics\x12*.com.augmentcode.rpc.GetDiagnosticsRequest\x1a+.com.augmentcode.rpc.GetDiagnosticsResponse\"\x00\x12Z\n\x10OpenMemoriesFile\x12,.com.augmentcode.rpc.OpenMemoriesFileRequest\x1a\x16.google.protobuf.Empty\"\x00\x12Z\n\x10OpenSettingsPage\x12,.com.augmentcode.rpc.OpenSettingsPageRequest\x1a\x16.google.protobuf.Empty\"\x00\x12\x64\n\x15UpdateGuidelinesState\x12\x31.com.augmentcode.rpc.UpdateGuidelinesStateRequest\x1a\x16.google.protobuf.Empty\"\x00\x12h\n\rChatSaveImage\x12).com.augmentcode.rpc.ChatSaveImageRequest\x1a*.com.augmentcode.rpc.ChatSaveImageResponse\"\x00\x12h\n\rChatLoadImage\x12).com.augmentcode.rpc.ChatLoadImageRequest\x1a*.com.augmentcode.rpc.ChatLoadImageResponse\"\x00\x12n\n\x0f\x43hatDeleteImage\x12+.com.augmentcode.rpc.ChatDeleteImageRequest\x1a,.com.augmentcode.rpc.ChatDeleteImageResponse\"\x00\x42\"\n\x13\x63om.augmentcode.rpcB\tChatTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'intellij_chat_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.augmentcode.rpcB\tChatTypesP\001'
  _globals['_SAVECHATDONERESPONSE']._loaded_options = None
  _globals['_SAVECHATDONERESPONSE']._serialized_options = b'\272\350t\016save-chat-done'
  _globals['_CHATLOADEDREQUEST']._loaded_options = None
  _globals['_CHATLOADEDREQUEST']._serialized_options = b'\272\350t\013chat-loaded'
  _globals['_CHATINITIALIZERESPONSE']._loaded_options = None
  _globals['_CHATINITIALIZERESPONSE']._serialized_options = b'\272\350t\017chat-initialize'
  _globals['_CHATINITIALIZERESPONSEDATA_MODELDISPLAYNAMETOIDENTRY']._loaded_options = None
  _globals['_CHATINITIALIZERESPONSEDATA_MODELDISPLAYNAMETOIDENTRY']._serialized_options = b'8\001'
  _globals['_CHATUSERMESSAGEREQUEST']._loaded_options = None
  _globals['_CHATUSERMESSAGEREQUEST']._serialized_options = b'\272\350t\021chat-user-message'
  _globals['_CHATMODELREPLY']._loaded_options = None
  _globals['_CHATMODELREPLY']._serialized_options = b'\272\350t\020chat-model-reply'
  _globals['_CHATUSERCANCELREQUEST']._loaded_options = None
  _globals['_CHATUSERCANCELREQUEST']._serialized_options = b'\272\350t\020chat-user-cancel'
  _globals['_SOURCEFOLDERSUPDATED']._loaded_options = None
  _globals['_SOURCEFOLDERSUPDATED']._serialized_options = b'\272\350t\026source-folders-updated'
  _globals['_FILERANGESSELECTED']._loaded_options = None
  _globals['_FILERANGESSELECTED']._serialized_options = b'\272\350t\024file-ranges-selected'
  _globals['_CURRENTLYOPENFILES']._loaded_options = None
  _globals['_CURRENTLYOPENFILES']._serialized_options = b'\272\350t\024currently-open-files'
  _globals['_FINDFILEREQUEST']._loaded_options = None
  _globals['_FINDFILEREQUEST']._serialized_options = b'\272\350t\021find-file-request'
  _globals['_RESOLVEFILEREQUEST']._loaded_options = None
  _globals['_RESOLVEFILEREQUEST']._serialized_options = b'\272\350t\024resolve-file-request'
  _globals['_RESOLVEFILERESPONSE']._loaded_options = None
  _globals['_RESOLVEFILERESPONSE']._serialized_options = b'\272\350t\025resolve-file-response'
  _globals['_FINDFILERESPONSE']._loaded_options = None
  _globals['_FINDFILERESPONSE']._serialized_options = b'\272\350t\022find-file-response'
  _globals['_SOURCEFOLDERSSYNCSTATUS']._loaded_options = None
  _globals['_SOURCEFOLDERSSYNCSTATUS']._serialized_options = b'\272\350t\032source-folders-sync-status'
  _globals['_OPENFILEREQUEST']._loaded_options = None
  _globals['_OPENFILEREQUEST']._serialized_options = b'\272\350t\topen-file'
  _globals['_CHATCREATEFILEREQUEST']._loaded_options = None
  _globals['_CHATCREATEFILEREQUEST']._serialized_options = b'\272\350t\020chat-create-file'
  _globals['_CHATRATINGMESSAGE']._loaded_options = None
  _globals['_CHATRATINGMESSAGE']._serialized_options = b'\272\350t\013chat-rating'
  _globals['_CHATRATINGDONEMESSAGE']._loaded_options = None
  _globals['_CHATRATINGDONEMESSAGE']._serialized_options = b'\272\350t\020chat-rating-done'
  _globals['_REPORTWEBVIEWCLIENTMETRICREQUEST']._loaded_options = None
  _globals['_REPORTWEBVIEWCLIENTMETRICREQUEST']._serialized_options = b'\272\350t\034report-webview-client-metric'
  _globals['_OPENCONFIRMATIONMODAL']._loaded_options = None
  _globals['_OPENCONFIRMATIONMODAL']._serialized_options = b'\272\350t\027open-confirmation-modal'
  _globals['_CONFIRMATIONMODALRESPONSE']._loaded_options = None
  _globals['_CONFIRMATIONMODALRESPONSE']._serialized_options = b'\272\350t\033confirmation-modal-response'
  _globals['_FINDEXTERNALSOURCESREQUEST']._loaded_options = None
  _globals['_FINDEXTERNALSOURCESREQUEST']._serialized_options = b'\272\350t\035find-external-sources-request'
  _globals['_FINDEXTERNALSOURCESRESPONSE']._loaded_options = None
  _globals['_FINDEXTERNALSOURCESRESPONSE']._serialized_options = b'\272\350t\036find-external-sources-response'
  _globals['_FINDSYMBOLREQUEST']._loaded_options = None
  _globals['_FINDSYMBOLREQUEST']._serialized_options = b'\272\350t\023find-symbol-request'
  _globals['_FINDSYMBOLREGEXREQUEST']._loaded_options = None
  _globals['_FINDSYMBOLREGEXREQUEST']._serialized_options = b'\272\350t\031find-symbol-regex-request'
  _globals['_FINDSYMBOLRESPONSE']._loaded_options = None
  _globals['_FINDSYMBOLRESPONSE']._serialized_options = b'\272\350t\024find-symbol-response'
  _globals['_FINDFOLDERREQUEST']._loaded_options = None
  _globals['_FINDFOLDERREQUEST']._serialized_options = b'\272\350t\023find-folder-request'
  _globals['_FINDFOLDERRESPONSE']._loaded_options = None
  _globals['_FINDFOLDERRESPONSE']._serialized_options = b'\272\350t\024find-folder-response'
  _globals['_FINDRECENTLYOPENEDFILESREQUEST']._loaded_options = None
  _globals['_FINDRECENTLYOPENEDFILESREQUEST']._serialized_options = b'\272\350t\032find-recently-opened-files'
  _globals['_FINDRECENTLYOPENEDFILESRESPONSE']._loaded_options = None
  _globals['_FINDRECENTLYOPENEDFILESRESPONSE']._serialized_options = b'\272\350t#find-recently-opened-files-response'
  _globals['_MAINPANELLOADED']._loaded_options = None
  _globals['_MAINPANELLOADED']._serialized_options = b'\272\350t\021main-panel-loaded'
  _globals['_MAINPANELDISPLAYAPP']._loaded_options = None
  _globals['_MAINPANELDISPLAYAPP']._serialized_options = b'\272\350t\026main-panel-display-app'
  _globals['_MAINPANELACTIONS']._loaded_options = None
  _globals['_MAINPANELACTIONS']._serialized_options = b'\272\350t\022main-panel-actions'
  _globals['_MAINPANELPERFORMACTION']._loaded_options = None
  _globals['_MAINPANELPERFORMACTION']._serialized_options = b'\272\350t\031main-panel-perform-action'
  _globals['_CHATSMARTPASTEREQUEST']._loaded_options = None
  _globals['_CHATSMARTPASTEREQUEST']._serialized_options = b'\272\350t\020chat-smart-paste'
  _globals['_USEDCHAT']._loaded_options = None
  _globals['_USEDCHAT']._serialized_options = b'\272\350t\tused-chat'
  _globals['_USEDSLASHACTION']._loaded_options = None
  _globals['_USEDSLASHACTION']._serialized_options = b'\272\350t\021used-slash-action'
  _globals['_SHOULDSHOWSUMMARY']._loaded_options = None
  _globals['_SHOULDSHOWSUMMARY']._serialized_options = b'\272\350t\023should-show-summary'
  _globals['_GETDIAGNOSTICSREQUEST']._loaded_options = None
  _globals['_GETDIAGNOSTICSREQUEST']._serialized_options = b'\272\350t\027get-diagnostics-request'
  _globals['_GETDIAGNOSTICSRESPONSE']._loaded_options = None
  _globals['_GETDIAGNOSTICSRESPONSE']._serialized_options = b'\272\350t\030get-diagnostics-response'
  _globals['_TOOLCHECKSAFEWEBVIEWREQUEST']._loaded_options = None
  _globals['_TOOLCHECKSAFEWEBVIEWREQUEST']._serialized_options = b'\272\350t\ncheck-safe'
  _globals['_TOOLCHECKSAFEWEBVIEWRESPONSE']._loaded_options = None
  _globals['_TOOLCHECKSAFEWEBVIEWRESPONSE']._serialized_options = b'\272\350t\023check-safe-response'
  _globals['_TOOLCALLWEBVIEWREQUEST']._loaded_options = None
  _globals['_TOOLCALLWEBVIEWREQUEST']._serialized_options = b'\272\350t\tcall-tool'
  _globals['_TOOLCALLWEBVIEWRESPONSE']._loaded_options = None
  _globals['_TOOLCALLWEBVIEWRESPONSE']._serialized_options = b'\272\350t\022call-tool-response'
  _globals['_TOOLCANCELRUNWEBVIEWREQUEST']._loaded_options = None
  _globals['_TOOLCANCELRUNWEBVIEWREQUEST']._serialized_options = b'\272\350t\017cancel-tool-run'
  _globals['_TOOLCANCELRUNWEBVIEWRESPONSE']._loaded_options = None
  _globals['_TOOLCANCELRUNWEBVIEWRESPONSE']._serialized_options = b'\272\350t\030cancel-tool-run-response'
  _globals['_CHECKTOOLEXISTSREQUEST']._loaded_options = None
  _globals['_CHECKTOOLEXISTSREQUEST']._serialized_options = b'\272\350t\017checkToolExists'
  _globals['_CHECKTOOLEXISTSRESPONSE']._loaded_options = None
  _globals['_CHECKTOOLEXISTSRESPONSE']._serialized_options = b'\272\350t\027checkToolExistsResponse'
  _globals['_CHATMODECHANGEDWEBVIEWREQUEST']._loaded_options = None
  _globals['_CHATMODECHANGEDWEBVIEWREQUEST']._serialized_options = b'\272\350t\021chat-mode-changed'
  _globals['_CHECKAGENTAUTOMODEAPPROVALREQUEST']._loaded_options = None
  _globals['_CHECKAGENTAUTOMODEAPPROVALREQUEST']._serialized_options = b'\272\350t\036check-agent-auto-mode-approval'
  _globals['_CHECKAGENTAUTOMODEAPPROVALRESPONSE']._loaded_options = None
  _globals['_CHECKAGENTAUTOMODEAPPROVALRESPONSE']._serialized_options = b'\272\350t\'check-agent-auto-mode-approval-response'
  _globals['_SETAGENTAUTOMODEAPPROVEDREQUEST']._loaded_options = None
  _globals['_SETAGENTAUTOMODEAPPROVEDREQUEST']._serialized_options = b'\272\350t\034set-agent-auto-mode-approved'
  _globals['_GETIDESTATENODEREQUEST']._loaded_options = None
  _globals['_GETIDESTATENODEREQUEST']._serialized_options = b'\272\350t\032get-ide-state-node-request'
  _globals['_GETIDESTATENODERESPONSE']._loaded_options = None
  _globals['_GETIDESTATENODERESPONSE']._serialized_options = b'\272\350t\033get-ide-state-node-response'
  _globals['_OPENMEMORIESFILEREQUEST']._loaded_options = None
  _globals['_OPENMEMORIESFILEREQUEST']._serialized_options = b'\272\350t\022open-memories-file'
  _globals['_GETWORKSPACEINFOREQUEST']._loaded_options = None
  _globals['_GETWORKSPACEINFOREQUEST']._serialized_options = b'\272\350t\032get-workspace-info-request'
  _globals['_GETWORKSPACEINFORESPONSE']._loaded_options = None
  _globals['_GETWORKSPACEINFORESPONSE']._serialized_options = b'\272\350t\033get-workspace-info-response'
  _globals['_UPDATEGUIDELINESSTATEREQUEST']._loaded_options = None
  _globals['_UPDATEGUIDELINESSTATEREQUEST']._serialized_options = b'\272\350t\027update-guidelines-state'
  _globals['_OPENSETTINGSPAGEREQUEST']._loaded_options = None
  _globals['_OPENSETTINGSPAGEREQUEST']._serialized_options = b'\272\350t\022open-settings-page'
  _globals['_CHATSAVEIMAGEREQUEST']._loaded_options = None
  _globals['_CHATSAVEIMAGEREQUEST']._serialized_options = b'\272\350t\027chat-save-image-request'
  _globals['_CHATSAVEIMAGERESPONSE']._loaded_options = None
  _globals['_CHATSAVEIMAGERESPONSE']._serialized_options = b'\272\350t\030chat-save-image-response'
  _globals['_CHATLOADIMAGEREQUEST']._loaded_options = None
  _globals['_CHATLOADIMAGEREQUEST']._serialized_options = b'\272\350t\027chat-load-image-request'
  _globals['_CHATLOADIMAGERESPONSE']._loaded_options = None
  _globals['_CHATLOADIMAGERESPONSE']._serialized_options = b'\272\350t\030chat-load-image-response'
  _globals['_CHATDELETEIMAGEREQUEST']._loaded_options = None
  _globals['_CHATDELETEIMAGEREQUEST']._serialized_options = b'\272\350t\031chat-delete-image-request'
  _globals['_CHATDELETEIMAGERESPONSE']._loaded_options = None
  _globals['_CHATDELETEIMAGERESPONSE']._serialized_options = b'\272\350t\032chat-delete-image-response'
  _globals['_SIGNINLOADED']._loaded_options = None
  _globals['_SIGNINLOADED']._serialized_options = b'\272\350t\016sign-in-loaded'
  _globals['_SIGNINLOADEDRESPONSE']._loaded_options = None
  _globals['_SIGNINLOADEDRESPONSE']._serialized_options = b'\272\350t\027sign-in-loaded-response'
  _globals['_AUGMENTLINKMESSAGE']._loaded_options = None
  _globals['_AUGMENTLINKMESSAGE']._serialized_options = b'\272\350t\014augment-link'
  _globals['_GETSUBSCRIPTIONINFOREQUEST']._loaded_options = None
  _globals['_GETSUBSCRIPTIONINFOREQUEST']._serialized_options = b'\272\350t\025get-subscription-info'
  _globals['_GETSUBSCRIPTIONINFORESPONSE']._loaded_options = None
  _globals['_GETSUBSCRIPTIONINFORESPONSE']._serialized_options = b'\272\350t\036get-subscription-info-response'
  _globals['_RULETYPE']._serialized_start=15543
  _globals['_RULETYPE']._serialized_end=15607
  _globals['_CHATREQUESTCONTENTNODETYPE']._serialized_start=15609
  _globals['_CHATREQUESTCONTENTNODETYPE']._serialized_end=15704
  _globals['_IMAGEFORMATTYPE']._serialized_start=15706
  _globals['_IMAGEFORMATTYPE']._serialized_end=15791
  _globals['_CHATRESULTNODETYPE']._serialized_start=15794
  _globals['_CHATRESULTNODETYPE']._serialized_end=15978
  _globals['_SYNCINGSTATUS']._serialized_start=15980
  _globals['_SYNCINGSTATUS']._serialized_end=16035
  _globals['_FEEDBACKRATING']._serialized_start=16037
  _globals['_FEEDBACKRATING']._serialized_end=16092
  _globals['_CHATMODE']._serialized_start=16094
  _globals['_CHATMODE']._serialized_end=16125
  _globals['_WEBVIEWNAME']._serialized_start=16127
  _globals['_WEBVIEWNAME']._serialized_end=16150
  _globals['_DIAGNOSTICSEVERITY']._serialized_start=16152
  _globals['_DIAGNOSTICSEVERITY']._serialized_end=16223
  _globals['_SAVECHATDONERESPONSE']._serialized_start=185
  _globals['_SAVECHATDONERESPONSE']._serialized_end=288
  _globals['_SAVECHATDONERESPONSEDATA']._serialized_start=290
  _globals['_SAVECHATDONERESPONSEDATA']._serialized_end=343
  _globals['_CHATLOADEDREQUEST']._serialized_start=345
  _globals['_CHATLOADEDREQUEST']._serialized_end=381
  _globals['_CHATINITIALIZERESPONSE']._serialized_start=383
  _globals['_CHATINITIALIZERESPONSE']._serialized_end=491
  _globals['_CHATINITIALIZERESPONSEDATA']._serialized_start=494
  _globals['_CHATINITIALIZERESPONSEDATA']._serialized_end=1593
  _globals['_CHATINITIALIZERESPONSEDATA_MODELDISPLAYNAMETOIDENTRY']._serialized_start=1534
  _globals['_CHATINITIALIZERESPONSEDATA_MODELDISPLAYNAMETOIDENTRY']._serialized_end=1593
  _globals['_CHATUSERMESSAGEREQUEST']._serialized_start=1595
  _globals['_CHATUSERMESSAGEREQUEST']._serialized_end=1698
  _globals['_CHATUSERMESSAGEDATA']._serialized_start=1701
  _globals['_CHATUSERMESSAGEDATA']._serialized_end=2314
  _globals['_ICHATACTIVECONTEXT']._serialized_start=2317
  _globals['_ICHATACTIVECONTEXT']._serialized_end=2694
  _globals['_RULE']._serialized_start=2696
  _globals['_RULE']._serialized_end=2799
  _globals['_CHATREQUESTTEXT']._serialized_start=2801
  _globals['_CHATREQUESTTEXT']._serialized_end=2835
  _globals['_CHATREQUESTIMAGE']._serialized_start=2837
  _globals['_CHATREQUESTIMAGE']._serialized_end=2929
  _globals['_CHATREQUESTCONTENTNODE']._serialized_start=2932
  _globals['_CHATREQUESTCONTENTNODE']._serialized_end=3148
  _globals['_CHATREQUESTTOOLRESULT']._serialized_start=3151
  _globals['_CHATREQUESTTOOLRESULT']._serialized_end=3407
  _globals['_IQUALIFIEDPATHNAME']._serialized_start=3409
  _globals['_IQUALIFIEDPATHNAME']._serialized_end=3464
  _globals['_CHATMODELREPLY']._serialized_start=3466
  _globals['_CHATMODELREPLY']._serialized_end=3591
  _globals['_CHATMODELREPLYDATA']._serialized_start=3594
  _globals['_CHATMODELREPLYDATA']._serialized_end=3860
  _globals['_CHATMODELREPLYERROR']._serialized_start=3862
  _globals['_CHATMODELREPLYERROR']._serialized_end=3954
  _globals['_CHATRESULT']._serialized_start=3957
  _globals['_CHATRESULT']._serialized_end=4200
  _globals['_WORKSPACEFILECHUNK']._serialized_start=4203
  _globals['_WORKSPACEFILECHUNK']._serialized_end=4364
  _globals['_CHATRESULTNODE']._serialized_start=4367
  _globals['_CHATRESULTNODE']._serialized_end=4651
  _globals['_CHATRESULTAGENTMEMORY']._serialized_start=4653
  _globals['_CHATRESULTAGENTMEMORY']._serialized_end=4693
  _globals['_CHATRESULTTOOLUSE']._serialized_start=4696
  _globals['_CHATRESULTTOOLUSE']._serialized_end=4883
  _globals['_CHATUSERCANCELREQUEST']._serialized_start=4885
  _globals['_CHATUSERCANCELREQUEST']._serialized_end=4985
  _globals['_CHATUSERCANCELDATA']._serialized_start=4987
  _globals['_CHATUSERCANCELDATA']._serialized_end=5026
  _globals['_SOURCEFOLDERSUPDATED']._serialized_start=5028
  _globals['_SOURCEFOLDERSUPDATED']._serialized_end=5139
  _globals['_SOURCEFOLDERSUPDATEDDATA']._serialized_start=5141
  _globals['_SOURCEFOLDERSUPDATEDDATA']._serialized_end=5230
  _globals['_ISOURCEFOLDERINFO']._serialized_start=5232
  _globals['_ISOURCEFOLDERINFO']._serialized_end=5271
  _globals['_FILERANGESSELECTED']._serialized_start=5273
  _globals['_FILERANGESSELECTED']._serialized_end=5367
  _globals['_CURRENTLYOPENFILES']._serialized_start=5369
  _globals['_CURRENTLYOPENFILES']._serialized_end=5463
  _globals['_FILEDETAILS']._serialized_start=5466
  _globals['_FILEDETAILS']._serialized_end=5967
  _globals['_RANGE']._serialized_start=5969
  _globals['_RANGE']._serialized_end=6005
  _globals['_FULLRANGE']._serialized_start=6007
  _globals['_FULLRANGE']._serialized_end=6106
  _globals['_LINECHANGES']._serialized_start=6108
  _globals['_LINECHANGES']._serialized_end=6191
  _globals['_LINECHANGE']._serialized_start=6193
  _globals['_LINECHANGE']._serialized_end=6293
  _globals['_FINDFILEREQUEST']._serialized_start=6295
  _globals['_FINDFILEREQUEST']._serialized_end=6391
  _globals['_FINDFILEREQUESTDATA']._serialized_start=6393
  _globals['_FINDFILEREQUESTDATA']._serialized_end=6489
  _globals['_RESOLVEFILEREQUEST']._serialized_start=6491
  _globals['_RESOLVEFILEREQUEST']._serialized_end=6593
  _globals['_RESOLVEFILERESPONSE']._serialized_start=6595
  _globals['_RESOLVEFILERESPONSE']._serialized_end=6691
  _globals['_FINDFILERESPONSE']._serialized_start=6693
  _globals['_FINDFILERESPONSE']._serialized_end=6783
  _globals['_SOURCEFOLDERSSYNCSTATUS']._serialized_start=6785
  _globals['_SOURCEFOLDERSSYNCSTATUS']._serialized_end=6897
  _globals['_SYNCINGSTATUSEVENT']._serialized_start=6900
  _globals['_SYNCINGSTATUSEVENT']._serialized_end=7047
  _globals['_SOURCEFOLDERSYNCINGPROGRESS']._serialized_start=7049
  _globals['_SOURCEFOLDERSYNCINGPROGRESS']._serialized_end=7154
  _globals['_SYNCINGPROGRESS']._serialized_start=7156
  _globals['_SYNCINGPROGRESS']._serialized_end=7238
  _globals['_OPENFILEREQUEST']._serialized_start=7240
  _globals['_OPENFILEREQUEST']._serialized_end=7320
  _globals['_CHATCREATEFILEREQUEST']._serialized_start=7322
  _globals['_CHATCREATEFILEREQUEST']._serialized_end=7429
  _globals['_CHATCREATEFILEREQUESTDATA']._serialized_start=7431
  _globals['_CHATCREATEFILEREQUESTDATA']._serialized_end=7489
  _globals['_CHATFEEDBACK']._serialized_start=7492
  _globals['_CHATFEEDBACK']._serialized_end=7637
  _globals['_CHATRATINGMESSAGE']._serialized_start=7639
  _globals['_CHATRATINGMESSAGE']._serialized_end=7724
  _globals['_CHATRATINGDONEMESSAGE']._serialized_start=7726
  _globals['_CHATRATINGDONEMESSAGE']._serialized_end=7820
  _globals['_REPORTWEBVIEWCLIENTMETRICREQUEST']._serialized_start=7823
  _globals['_REPORTWEBVIEWCLIENTMETRICREQUEST']._serialized_end=7983
  _globals['_OPENCONFIRMATIONMODAL']._serialized_start=7985
  _globals['_OPENCONFIRMATIONMODAL']._serialized_end=8099
  _globals['_OPENCONFIRMATIONMODALDATA']._serialized_start=8101
  _globals['_OPENCONFIRMATIONMODALDATA']._serialized_end=8213
  _globals['_CONFIRMATIONMODALRESPONSE']._serialized_start=8215
  _globals['_CONFIRMATIONMODALRESPONSE']._serialized_end=8341
  _globals['_CONFIRMATIONMODALRESPONSEDATA']._serialized_start=8343
  _globals['_CONFIRMATIONMODALRESPONSEDATA']._serialized_end=8401
  _globals['_SEARCHEXTERNALSOURCESREQUEST']._serialized_start=8403
  _globals['_SEARCHEXTERNALSOURCESREQUEST']._serialized_end=8470
  _globals['_SEARCHEXTERNALSOURCESRESPONSE']._serialized_start=8472
  _globals['_SEARCHEXTERNALSOURCESRESPONSE']._serialized_end=8557
  _globals['_FINDEXTERNALSOURCESREQUEST']._serialized_start=8560
  _globals['_FINDEXTERNALSOURCESREQUEST']._serialized_end=8688
  _globals['_FINDEXTERNALSOURCESRESPONSE']._serialized_start=8691
  _globals['_FINDEXTERNALSOURCESRESPONSE']._serialized_end=8822
  _globals['_EXTERNALSOURCE']._serialized_start=8824
  _globals['_EXTERNALSOURCE']._serialized_end=8902
  _globals['_FINDSYMBOLREQUEST']._serialized_start=8904
  _globals['_FINDSYMBOLREQUEST']._serialized_end=9006
  _globals['_FINDSYMBOLREQUESTDATA']._serialized_start=9008
  _globals['_FINDSYMBOLREQUESTDATA']._serialized_end=9106
  _globals['_ISEARCHSCOPEARGS']._serialized_start=9108
  _globals['_ISEARCHSCOPEARGS']._serialized_end=9175
  _globals['_FINDSYMBOLREGEXREQUEST']._serialized_start=9177
  _globals['_FINDSYMBOLREGEXREQUEST']._serialized_end=9290
  _globals['_FINDSYMBOLRESPONSEDATA']._serialized_start=9292
  _globals['_FINDSYMBOLRESPONSEDATA']._serialized_end=9364
  _globals['_FINDSYMBOLRESPONSE']._serialized_start=9366
  _globals['_FINDSYMBOLRESPONSE']._serialized_end=9471
  _globals['_FINDFOLDERREQUEST']._serialized_start=9473
  _globals['_FINDFOLDERREQUEST']._serialized_end=9575
  _globals['_FINDFOLDERREQUESTDATA']._serialized_start=9577
  _globals['_FINDFOLDERREQUESTDATA']._serialized_end=9675
  _globals['_FINDFOLDERRESPONSE']._serialized_start=9677
  _globals['_FINDFOLDERRESPONSE']._serialized_end=9771
  _globals['_FINDRECENTLYOPENEDFILESREQUEST']._serialized_start=9773
  _globals['_FINDRECENTLYOPENEDFILESREQUEST']._serialized_end=9893
  _globals['_FINDRECENTLYOPENEDFILESRESPONSE']._serialized_start=9895
  _globals['_FINDRECENTLYOPENEDFILESRESPONSE']._serialized_end=10017
  _globals['_MAINPANELLOADED']._serialized_start=10019
  _globals['_MAINPANELLOADED']._serialized_end=10059
  _globals['_MAINPANELDISPLAYAPP']._serialized_start=10061
  _globals['_MAINPANELDISPLAYAPP']._serialized_end=10124
  _globals['_MAINPANELACTIONS']._serialized_start=10126
  _globals['_MAINPANELACTIONS']._serialized_end=10182
  _globals['_MAINPANELPERFORMACTION']._serialized_start=10184
  _globals['_MAINPANELPERFORMACTION']._serialized_end=10253
  _globals['_CHATSMARTPASTEREQUEST']._serialized_start=10255
  _globals['_CHATSMARTPASTEREQUEST']._serialized_end=10355
  _globals['_CHATSMARTPASTEDATA']._serialized_start=10358
  _globals['_CHATSMARTPASTEDATA']._serialized_end=10554
  _globals['_CHATSMARTPASTEOPTIONS']._serialized_start=10556
  _globals['_CHATSMARTPASTEOPTIONS']._serialized_end=10645
  _globals['_USEDCHAT']._serialized_start=10647
  _globals['_USEDCHAT']._serialized_end=10672
  _globals['_USEDSLASHACTION']._serialized_start=10674
  _globals['_USEDSLASHACTION']._serialized_end=10714
  _globals['_SHOULDSHOWSUMMARY']._serialized_start=10716
  _globals['_SHOULDSHOWSUMMARY']._serialized_end=10760
  _globals['_GETDIAGNOSTICSREQUEST']._serialized_start=10762
  _globals['_GETDIAGNOSTICSREQUEST']._serialized_end=10814
  _globals['_GETDIAGNOSTICSRESPONSE']._serialized_start=10816
  _globals['_GETDIAGNOSTICSRESPONSE']._serialized_end=10917
  _globals['_DIAGNOSTIC']._serialized_start=10920
  _globals['_DIAGNOSTIC']._serialized_end=11098
  _globals['_DIAGNOSTICFILELOCATION']._serialized_start=11100
  _globals['_DIAGNOSTICFILELOCATION']._serialized_end=11176
  _globals['_CHATINSTRUCTIONSTREAMRESULT']._serialized_start=11179
  _globals['_CHATINSTRUCTIONSTREAMRESULT']._serialized_end=11577
  _globals['_TOOLCHECKSAFEWEBVIEWREQUEST']._serialized_start=11579
  _globals['_TOOLCHECKSAFEWEBVIEWREQUEST']._serialized_end=11678
  _globals['_TOOLCHECKSAFEDATA']._serialized_start=11680
  _globals['_TOOLCHECKSAFEDATA']._serialized_end=11753
  _globals['_TOOLCHECKSAFEWEBVIEWRESPONSE']._serialized_start=11755
  _globals['_TOOLCHECKSAFEWEBVIEWRESPONSE']._serialized_end=11872
  _globals['_TOOLCHECKSAFERESPONSEDATA']._serialized_start=11874
  _globals['_TOOLCHECKSAFERESPONSEDATA']._serialized_end=11917
  _globals['_TOOLCALLWEBVIEWREQUEST']._serialized_start=11919
  _globals['_TOOLCALLWEBVIEWREQUEST']._serialized_end=12014
  _globals['_TOOLCALLREQUESTDATA']._serialized_start=12017
  _globals['_TOOLCALLREQUESTDATA']._serialized_end=12226
  _globals['_TOOLCALLWEBVIEWRESPONSE']._serialized_start=12228
  _globals['_TOOLCALLWEBVIEWRESPONSE']._serialized_end=12341
  _globals['_TOOLCALLWEBVIEWRESPONSEDATA']._serialized_start=12343
  _globals['_TOOLCALLWEBVIEWRESPONSEDATA']._serialized_end=12441
  _globals['_TOOLCANCELRUNWEBVIEWREQUEST']._serialized_start=12443
  _globals['_TOOLCANCELRUNWEBVIEWREQUEST']._serialized_end=12547
  _globals['_TOOLCANCELRUNDATA']._serialized_start=12549
  _globals['_TOOLCANCELRUNDATA']._serialized_end=12606
  _globals['_TOOLCANCELRUNWEBVIEWRESPONSE']._serialized_start=12608
  _globals['_TOOLCANCELRUNWEBVIEWRESPONSE']._serialized_end=12668
  _globals['_CHECKTOOLEXISTSREQUEST']._serialized_start=12670
  _globals['_CHECKTOOLEXISTSREQUEST']._serialized_end=12733
  _globals['_CHECKTOOLEXISTSRESPONSE']._serialized_start=12735
  _globals['_CHECKTOOLEXISTSRESPONSE']._serialized_end=12805
  _globals['_CHATMODECHANGEDWEBVIEWREQUEST']._serialized_start=12807
  _globals['_CHATMODECHANGEDWEBVIEWREQUEST']._serialized_end=12917
  _globals['_CHATMODECHANGEDDATA']._serialized_start=12919
  _globals['_CHATMODECHANGEDDATA']._serialized_end=12954
  _globals['_CHECKAGENTAUTOMODEAPPROVALREQUEST']._serialized_start=12956
  _globals['_CHECKAGENTAUTOMODEAPPROVALREQUEST']._serialized_end=13027
  _globals['_CHECKAGENTAUTOMODEAPPROVALRESPONSE']._serialized_start=13029
  _globals['_CHECKAGENTAUTOMODEAPPROVALRESPONSE']._serialized_end=13124
  _globals['_SETAGENTAUTOMODEAPPROVEDREQUEST']._serialized_start=13126
  _globals['_SETAGENTAUTOMODEAPPROVEDREQUEST']._serialized_end=13207
  _globals['_GETIDESTATENODEREQUEST']._serialized_start=13209
  _globals['_GETIDESTATENODEREQUEST']._serialized_end=13265
  _globals['_GETIDESTATENODERESPONSE']._serialized_start=13267
  _globals['_GETIDESTATENODERESPONSE']._serialized_end=13394
  _globals['_OPENMEMORIESFILEREQUEST']._serialized_start=13396
  _globals['_OPENMEMORIESFILEREQUEST']._serialized_end=13445
  _globals['_GETWORKSPACEINFOREQUEST']._serialized_start=13447
  _globals['_GETWORKSPACEINFOREQUEST']._serialized_end=13504
  _globals['_GETWORKSPACEINFORESPONSE']._serialized_start=13506
  _globals['_GETWORKSPACEINFORESPONSE']._serialized_end=13630
  _globals['_GETWORKSPACEINFORESPONSEDATA']._serialized_start=13632
  _globals['_GETWORKSPACEINFORESPONSEDATA']._serialized_end=13688
  _globals['_USERGUIDELINESSTATE']._serialized_start=13690
  _globals['_USERGUIDELINESSTATE']._serialized_end=13748
  _globals['_WORKSPACEGUIDELINESSTATE']._serialized_start=13750
  _globals['_WORKSPACEGUIDELINESSTATE']._serialized_end=13841
  _globals['_GUIDELINESSTATES']._serialized_start=13844
  _globals['_GUIDELINESSTATES']._serialized_end=14004
  _globals['_UPDATEGUIDELINESSTATEREQUEST']._serialized_start=14006
  _globals['_UPDATEGUIDELINESSTATEREQUEST']._serialized_end=14118
  _globals['_OPENSETTINGSPAGEREQUEST']._serialized_start=14120
  _globals['_OPENSETTINGSPAGEREQUEST']._serialized_end=14183
  _globals['_FILEDATA']._serialized_start=14185
  _globals['_FILEDATA']._serialized_end=14227
  _globals['_CHATSAVEIMAGEREQUEST']._serialized_start=14229
  _globals['_CHATSAVEIMAGEREQUEST']._serialized_end=14325
  _globals['_CHATSAVEIMAGERESPONSE']._serialized_start=14327
  _globals['_CHATSAVEIMAGERESPONSE']._serialized_end=14394
  _globals['_CHATLOADIMAGEREQUEST']._serialized_start=14396
  _globals['_CHATLOADIMAGEREQUEST']._serialized_end=14461
  _globals['_CHATLOADIMAGERESPONSE']._serialized_start=14463
  _globals['_CHATLOADIMAGERESPONSE']._serialized_end=14530
  _globals['_CHATDELETEIMAGEREQUEST']._serialized_start=14532
  _globals['_CHATDELETEIMAGEREQUEST']._serialized_end=14601
  _globals['_CHATDELETEIMAGERESPONSE']._serialized_start=14603
  _globals['_CHATDELETEIMAGERESPONSE']._serialized_end=14660
  _globals['_SIGNINLOADED']._serialized_start=14662
  _globals['_SIGNINLOADED']._serialized_end=14696
  _globals['_SIGNINLOADEDRESPONSE']._serialized_start=14698
  _globals['_SIGNINLOADEDRESPONSE']._serialized_end=14810
  _globals['_SIGNINLOADEDRESPONSEDATA']._serialized_start=14812
  _globals['_SIGNINLOADEDRESPONSEDATA']._serialized_end=14854
  _globals['_AUGMENTLINKMESSAGE']._serialized_start=14856
  _globals['_AUGMENTLINKMESSAGE']._serialized_end=14908
  _globals['_MEMORIESINFO']._serialized_start=14910
  _globals['_MEMORIESINFO']._serialized_end=14973
  _globals['_GETSUBSCRIPTIONINFOREQUEST']._serialized_start=14975
  _globals['_GETSUBSCRIPTIONINFOREQUEST']._serialized_end=15030
  _globals['_GETSUBSCRIPTIONINFORESPONSE']._serialized_start=15033
  _globals['_GETSUBSCRIPTIONINFORESPONSE']._serialized_end=15166
  _globals['_GETSUBSCRIPTIONINFORESPONSEDATA']._serialized_start=15169
  _globals['_GETSUBSCRIPTIONINFORESPONSEDATA']._serialized_end=15407
  _globals['_SUBSCRIPTIONENTERPRISE']._serialized_start=15409
  _globals['_SUBSCRIPTIONENTERPRISE']._serialized_end=15433
  _globals['_INACTIVESUBSCRIPTION']._serialized_start=15435
  _globals['_INACTIVESUBSCRIPTION']._serialized_end=15457
  _globals['_SUBSCRIPTIONINFO']._serialized_start=15459
  _globals['_SUBSCRIPTIONINFO']._serialized_end=15541
  _globals['_WEBVIEWCHATSERVICE']._serialized_start=16226
  _globals['_WEBVIEWCHATSERVICE']._serialized_end=18597
# @@protoc_insertion_point(module_scope)
