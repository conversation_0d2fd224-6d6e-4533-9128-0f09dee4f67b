# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
import intellij_chat_pb2 as intellij__chat__pb2
import intellij_settings_pb2 as intellij__settings__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in intellij_settings_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WebviewSettingsServiceStub(object):
    """
    This service defines an abstraction of an interface that the settings web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ToolConfigLoaded = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigLoaded',
                request_serializer=intellij__settings__pb2.ToolConfigLoadedRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.ToolConfigInitializeResponse.FromString,
                _registered_method=True)
        self.ToolConfigGetDefinitions = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigGetDefinitions',
                request_serializer=intellij__settings__pb2.ToolConfigGetDefinitionsRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.ToolConfigDefinitionsResponse.FromString,
                _registered_method=True)
        self.ToolConfigSave = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigSave',
                request_serializer=intellij__settings__pb2.ToolConfigSaveRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ToolConfigStartOAuth = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigStartOAuth',
                request_serializer=intellij__settings__pb2.ToolConfigStartOAuthRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.ToolConfigStartOAuthResponse.FromString,
                _registered_method=True)
        self.ToolConfigRevokeAccess = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigRevokeAccess',
                request_serializer=intellij__settings__pb2.ToolConfigRevokeAccessRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.ToolConfigDefinitionsResponse.FromString,
                _registered_method=True)
        self.GetStoredMCPServers = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/GetStoredMCPServers',
                request_serializer=intellij__settings__pb2.GetStoredMCPServersRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.GetStoredMCPServersResponse.FromString,
                _registered_method=True)
        self.SetStoredMCPServers = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/SetStoredMCPServers',
                request_serializer=intellij__settings__pb2.SetStoredMCPServersRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ExecuteInitialOrientation = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ExecuteInitialOrientation',
                request_serializer=intellij__settings__pb2.ExecuteInitialOrientationRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.UpdateUserGuidelines = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/UpdateUserGuidelines',
                request_serializer=intellij__settings__pb2.UpdateUserGuidelinesRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.SignOut = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/SignOut',
                request_serializer=intellij__settings__pb2.SignOutRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.OpenFile = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/OpenFile',
                request_serializer=intellij__chat__pb2.OpenFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ShowNotification = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/ShowNotification',
                request_serializer=intellij__settings__pb2.ShowNotificationRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.OpenConfirmationModal = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/OpenConfirmationModal',
                request_serializer=intellij__chat__pb2.OpenConfirmationModal.SerializeToString,
                response_deserializer=intellij__chat__pb2.ConfirmationModalResponse.FromString,
                _registered_method=True)
        self.HandleTriggerImportDialog = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewSettingsService/HandleTriggerImportDialog',
                request_serializer=intellij__settings__pb2.HandleTriggerImportDialogRequest.SerializeToString,
                response_deserializer=intellij__settings__pb2.HandleTriggerImportDialogResponse.FromString,
                _registered_method=True)


class WebviewSettingsServiceServicer(object):
    """
    This service defines an abstraction of an interface that the settings web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def ToolConfigLoaded(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToolConfigGetDefinitions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToolConfigSave(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToolConfigStartOAuth(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToolConfigRevokeAccess(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStoredMCPServers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetStoredMCPServers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ExecuteInitialOrientation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateUserGuidelines(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SignOut(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ShowNotification(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OpenConfirmationModal(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HandleTriggerImportDialog(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WebviewSettingsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ToolConfigLoaded': grpc.unary_unary_rpc_method_handler(
                    servicer.ToolConfigLoaded,
                    request_deserializer=intellij__settings__pb2.ToolConfigLoadedRequest.FromString,
                    response_serializer=intellij__settings__pb2.ToolConfigInitializeResponse.SerializeToString,
            ),
            'ToolConfigGetDefinitions': grpc.unary_unary_rpc_method_handler(
                    servicer.ToolConfigGetDefinitions,
                    request_deserializer=intellij__settings__pb2.ToolConfigGetDefinitionsRequest.FromString,
                    response_serializer=intellij__settings__pb2.ToolConfigDefinitionsResponse.SerializeToString,
            ),
            'ToolConfigSave': grpc.unary_unary_rpc_method_handler(
                    servicer.ToolConfigSave,
                    request_deserializer=intellij__settings__pb2.ToolConfigSaveRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ToolConfigStartOAuth': grpc.unary_unary_rpc_method_handler(
                    servicer.ToolConfigStartOAuth,
                    request_deserializer=intellij__settings__pb2.ToolConfigStartOAuthRequest.FromString,
                    response_serializer=intellij__settings__pb2.ToolConfigStartOAuthResponse.SerializeToString,
            ),
            'ToolConfigRevokeAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.ToolConfigRevokeAccess,
                    request_deserializer=intellij__settings__pb2.ToolConfigRevokeAccessRequest.FromString,
                    response_serializer=intellij__settings__pb2.ToolConfigDefinitionsResponse.SerializeToString,
            ),
            'GetStoredMCPServers': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStoredMCPServers,
                    request_deserializer=intellij__settings__pb2.GetStoredMCPServersRequest.FromString,
                    response_serializer=intellij__settings__pb2.GetStoredMCPServersResponse.SerializeToString,
            ),
            'SetStoredMCPServers': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStoredMCPServers,
                    request_deserializer=intellij__settings__pb2.SetStoredMCPServersRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ExecuteInitialOrientation': grpc.unary_unary_rpc_method_handler(
                    servicer.ExecuteInitialOrientation,
                    request_deserializer=intellij__settings__pb2.ExecuteInitialOrientationRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'UpdateUserGuidelines': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateUserGuidelines,
                    request_deserializer=intellij__settings__pb2.UpdateUserGuidelinesRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SignOut': grpc.unary_unary_rpc_method_handler(
                    servicer.SignOut,
                    request_deserializer=intellij__settings__pb2.SignOutRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'OpenFile': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenFile,
                    request_deserializer=intellij__chat__pb2.OpenFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ShowNotification': grpc.unary_unary_rpc_method_handler(
                    servicer.ShowNotification,
                    request_deserializer=intellij__settings__pb2.ShowNotificationRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'OpenConfirmationModal': grpc.unary_unary_rpc_method_handler(
                    servicer.OpenConfirmationModal,
                    request_deserializer=intellij__chat__pb2.OpenConfirmationModal.FromString,
                    response_serializer=intellij__chat__pb2.ConfirmationModalResponse.SerializeToString,
            ),
            'HandleTriggerImportDialog': grpc.unary_unary_rpc_method_handler(
                    servicer.HandleTriggerImportDialog,
                    request_deserializer=intellij__settings__pb2.HandleTriggerImportDialogRequest.FromString,
                    response_serializer=intellij__settings__pb2.HandleTriggerImportDialogResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.augmentcode.rpc.WebviewSettingsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.augmentcode.rpc.WebviewSettingsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WebviewSettingsService(object):
    """
    This service defines an abstraction of an interface that the settings web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    @staticmethod
    def ToolConfigLoaded(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigLoaded',
            intellij__settings__pb2.ToolConfigLoadedRequest.SerializeToString,
            intellij__settings__pb2.ToolConfigInitializeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToolConfigGetDefinitions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigGetDefinitions',
            intellij__settings__pb2.ToolConfigGetDefinitionsRequest.SerializeToString,
            intellij__settings__pb2.ToolConfigDefinitionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToolConfigSave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigSave',
            intellij__settings__pb2.ToolConfigSaveRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToolConfigStartOAuth(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigStartOAuth',
            intellij__settings__pb2.ToolConfigStartOAuthRequest.SerializeToString,
            intellij__settings__pb2.ToolConfigStartOAuthResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ToolConfigRevokeAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ToolConfigRevokeAccess',
            intellij__settings__pb2.ToolConfigRevokeAccessRequest.SerializeToString,
            intellij__settings__pb2.ToolConfigDefinitionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStoredMCPServers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/GetStoredMCPServers',
            intellij__settings__pb2.GetStoredMCPServersRequest.SerializeToString,
            intellij__settings__pb2.GetStoredMCPServersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetStoredMCPServers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/SetStoredMCPServers',
            intellij__settings__pb2.SetStoredMCPServersRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ExecuteInitialOrientation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ExecuteInitialOrientation',
            intellij__settings__pb2.ExecuteInitialOrientationRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateUserGuidelines(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/UpdateUserGuidelines',
            intellij__settings__pb2.UpdateUserGuidelinesRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SignOut(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/SignOut',
            intellij__settings__pb2.SignOutRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/OpenFile',
            intellij__chat__pb2.OpenFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ShowNotification(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/ShowNotification',
            intellij__settings__pb2.ShowNotificationRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OpenConfirmationModal(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/OpenConfirmationModal',
            intellij__chat__pb2.OpenConfirmationModal.SerializeToString,
            intellij__chat__pb2.ConfirmationModalResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HandleTriggerImportDialog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewSettingsService/HandleTriggerImportDialog',
            intellij__settings__pb2.HandleTriggerImportDialogRequest.SerializeToString,
            intellij__settings__pb2.HandleTriggerImportDialogResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
