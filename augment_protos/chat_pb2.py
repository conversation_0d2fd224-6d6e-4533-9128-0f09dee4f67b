# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: chat.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'chat.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nchat.proto\x12 com.augmentcode.sidecar.rpc.chat\"\xe9\x01\n\x0f\x43hatHistoryItem\x12\x17\n\x0frequest_message\x18\x01 \x01(\t\x12\x15\n\rresponse_text\x18\x02 \x01(\t\x12\x12\n\nrequest_id\x18\x03 \x01(\t\x12H\n\rrequest_nodes\x18\x04 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatRequestNode\x12H\n\x0eresponse_nodes\x18\x05 \x03(\x0b\x32\x30.com.augmentcode.sidecar.rpc.chat.ChatResultNode\"\x98\x04\n\x0f\x43hatRequestNode\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12I\n\ttext_node\x18\x03 \x01(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatRequestTextH\x00\x88\x01\x01\x12V\n\x10tool_result_node\x18\x04 \x01(\x0b\x32\x37.com.augmentcode.sidecar.rpc.chat.ChatRequestToolResultH\x01\x88\x01\x01\x12K\n\nimage_node\x18\x05 \x01(\x0b\x32\x32.com.augmentcode.sidecar.rpc.chat.ChatRequestImageH\x02\x88\x01\x01\x12P\n\rimage_id_node\x18\x06 \x01(\x0b\x32\x34.com.augmentcode.sidecar.rpc.chat.ChatRequestImageIdH\x03\x88\x01\x01\x12R\n\x0eide_state_node\x18\x07 \x01(\x0b\x32\x35.com.augmentcode.sidecar.rpc.chat.ChatRequestIdeStateH\x04\x88\x01\x01\x42\x0c\n\n_text_nodeB\x13\n\x11_tool_result_nodeB\r\n\x0b_image_nodeB\x10\n\x0e_image_id_nodeB\x11\n\x0f_ide_state_node\"\xf0\x01\n\x13\x43hatRequestIdeState\x12P\n\x11workspace_folders\x18\x01 \x03(\x0b\x32\x35.com.augmentcode.sidecar.rpc.chat.WorkspaceFolderInfo\x12#\n\x1bworkspace_folders_unchanged\x18\x02 \x01(\x08\x12M\n\x10\x63urrent_terminal\x18\x03 \x01(\x0b\x32..com.augmentcode.sidecar.rpc.chat.TerminalInfoH\x00\x88\x01\x01\x42\x13\n\x11_current_terminal\"C\n\x13WorkspaceFolderInfo\x12\x17\n\x0frepository_root\x18\x01 \x01(\t\x12\x13\n\x0b\x66older_root\x18\x02 \x01(\t\"F\n\x0cTerminalInfo\x12\x13\n\x0bterminal_id\x18\x01 \x01(\x05\x12!\n\x19\x63urrent_working_directory\x18\x02 \x01(\t\"n\n\x10\x43hatRequestImage\x12\x17\n\nimage_data\x18\x01 \x01(\tB\x03\x80\x01\x01\x12\x41\n\x06\x66ormat\x18\x02 \x01(\x0e\x32\x31.com.augmentcode.sidecar.rpc.chat.ImageFormatType\"i\n\x12\x43hatRequestImageId\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12\x41\n\x06\x66ormat\x18\x02 \x01(\x0e\x32\x31.com.augmentcode.sidecar.rpc.chat.ImageFormatType\"\"\n\x0f\x43hatRequestText\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"\xf2\x01\n\x16\x43hatRequestContentNode\x12J\n\x04type\x18\x01 \x01(\x0e\x32<.com.augmentcode.sidecar.rpc.chat.ChatRequestContentNodeType\x12\x19\n\x0ctext_content\x18\x02 \x01(\tH\x00\x88\x01\x01\x12N\n\rimage_content\x18\x03 \x01(\x0b\x32\x32.com.augmentcode.sidecar.rpc.chat.ChatRequestImageH\x01\x88\x01\x01\x42\x0f\n\r_text_contentB\x10\n\x0e_image_content\"\x9a\x02\n\x15\x43hatRequestToolResult\x12\x13\n\x0btool_use_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x10\n\x08is_error\x18\x03 \x01(\x08\x12\x17\n\nrequest_id\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x46\n\x05image\x18\x05 \x01(\x0b\x32\x32.com.augmentcode.sidecar.rpc.chat.ChatRequestImageH\x01\x88\x01\x01\x12O\n\rcontent_nodes\x18\x06 \x03(\x0b\x32\x38.com.augmentcode.sidecar.rpc.chat.ChatRequestContentNodeB\r\n\x0b_request_idB\x08\n\x06_image\"\xc3\x02\n\x0e\x43hatResultNode\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x42\n\x04type\x18\x02 \x01(\x0e\x32\x34.com.augmentcode.sidecar.rpc.chat.ChatResultNodeType\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12S\n\x07toolUse\x18\x04 \x01(\x0b\x32\x33.com.augmentcode.sidecar.rpc.chat.ChatResultToolUseH\x00R\x08tool_use\x88\x01\x01\x12_\n\x0b\x61gentMemory\x18\x05 \x01(\x0b\x32\x37.com.augmentcode.sidecar.rpc.chat.ChatResultAgentMemoryH\x01R\x0c\x61gent_memory\x88\x01\x01\x42\n\n\x08_toolUseB\x0e\n\x0c_agentMemory\"\xbb\x01\n\x11\x43hatResultToolUse\x12\x1e\n\ttoolUseId\x18\x01 \x01(\tR\x0btool_use_id\x12\x1b\n\x08toolName\x18\x02 \x01(\tR\ttool_name\x12\x1d\n\tinputJson\x18\x03 \x01(\tR\ninput_json\x12&\n\rmcpServerName\x18\x04 \x01(\tR\x0fmcp_server_name\x12\"\n\x0bmcpToolName\x18\x05 \x01(\tR\rmcp_tool_name\"(\n\x15\x43hatResultAgentMemory\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"F\n\x11\x43hatStreamRequest\x12\x15\n\rprogressToken\x18\x01 \x01(\t\x12\x1a\n\x12requestPayloadJson\x18\x02 \x01(\t\"1\n\x12\x43hatStreamResponse\x12\x1b\n\x13responsePayloadJson\x18\x01 \x01(\t*}\n\x13\x43hatRequestNodeType\x12\x08\n\x04TEXT\x10\x00\x12\x0f\n\x0bTOOL_RESULT\x10\x01\x12\t\n\x05IMAGE\x10\x02\x12\x0c\n\x08IMAGE_ID\x10\x03\x12\r\n\tIDE_STATE\x10\x04\x12\x0f\n\x0b\x45\x44IT_EVENTS\x10\x05\x12\x12\n\x0e\x43HECKPOINT_REF\x10\x06*U\n\x0fImageFormatType\x12\x1c\n\x18IMAGE_FORMAT_UNSPECIFIED\x10\x00\x12\x07\n\x03PNG\x10\x01\x12\x08\n\x04JPEG\x10\x02\x12\x07\n\x03GIF\x10\x03\x12\x08\n\x04WEBP\x10\x04*_\n\x1a\x43hatRequestContentNodeType\x12\x1c\n\x18\x43ONTENT_TYPE_UNSPECIFIED\x10\x00\x12\x10\n\x0c\x43ONTENT_TEXT\x10\x01\x12\x11\n\rCONTENT_IMAGE\x10\x02*\xb8\x01\n\x12\x43hatResultNodeType\x12\x10\n\x0cRAW_RESPONSE\x10\x00\x12\x17\n\x13SUGGESTED_QUESTIONS\x10\x01\x12\x16\n\x12MAIN_TEXT_FINISHED\x10\x02\x12\x19\n\x15WORKSPACE_FILE_CHUNKS\x10\x03\x12\x14\n\x10RELEVANT_SOURCES\x10\x04\x12\x0c\n\x08TOOL_USE\x10\x05\x12\x12\n\x0eTOOL_USE_START\x10\x07\x12\x0c\n\x08THINKING\x10\x08\x42\x32\n com.augmentcode.sidecar.rpc.chatB\x0c\x43hatRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'chat_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.augmentcode.sidecar.rpc.chatB\014ChatRPCTypesP\001'
  _globals['_CHATREQUESTIMAGE'].fields_by_name['image_data']._loaded_options = None
  _globals['_CHATREQUESTIMAGE'].fields_by_name['image_data']._serialized_options = b'\200\001\001'
  _globals['_CHATREQUESTNODETYPE']._serialized_start=2673
  _globals['_CHATREQUESTNODETYPE']._serialized_end=2798
  _globals['_IMAGEFORMATTYPE']._serialized_start=2800
  _globals['_IMAGEFORMATTYPE']._serialized_end=2885
  _globals['_CHATREQUESTCONTENTNODETYPE']._serialized_start=2887
  _globals['_CHATREQUESTCONTENTNODETYPE']._serialized_end=2982
  _globals['_CHATRESULTNODETYPE']._serialized_start=2985
  _globals['_CHATRESULTNODETYPE']._serialized_end=3169
  _globals['_CHATHISTORYITEM']._serialized_start=49
  _globals['_CHATHISTORYITEM']._serialized_end=282
  _globals['_CHATREQUESTNODE']._serialized_start=285
  _globals['_CHATREQUESTNODE']._serialized_end=821
  _globals['_CHATREQUESTIDESTATE']._serialized_start=824
  _globals['_CHATREQUESTIDESTATE']._serialized_end=1064
  _globals['_WORKSPACEFOLDERINFO']._serialized_start=1066
  _globals['_WORKSPACEFOLDERINFO']._serialized_end=1133
  _globals['_TERMINALINFO']._serialized_start=1135
  _globals['_TERMINALINFO']._serialized_end=1205
  _globals['_CHATREQUESTIMAGE']._serialized_start=1207
  _globals['_CHATREQUESTIMAGE']._serialized_end=1317
  _globals['_CHATREQUESTIMAGEID']._serialized_start=1319
  _globals['_CHATREQUESTIMAGEID']._serialized_end=1424
  _globals['_CHATREQUESTTEXT']._serialized_start=1426
  _globals['_CHATREQUESTTEXT']._serialized_end=1460
  _globals['_CHATREQUESTCONTENTNODE']._serialized_start=1463
  _globals['_CHATREQUESTCONTENTNODE']._serialized_end=1705
  _globals['_CHATREQUESTTOOLRESULT']._serialized_start=1708
  _globals['_CHATREQUESTTOOLRESULT']._serialized_end=1990
  _globals['_CHATRESULTNODE']._serialized_start=1993
  _globals['_CHATRESULTNODE']._serialized_end=2316
  _globals['_CHATRESULTTOOLUSE']._serialized_start=2319
  _globals['_CHATRESULTTOOLUSE']._serialized_end=2506
  _globals['_CHATRESULTAGENTMEMORY']._serialized_start=2508
  _globals['_CHATRESULTAGENTMEMORY']._serialized_end=2548
  _globals['_CHATSTREAMREQUEST']._serialized_start=2550
  _globals['_CHATSTREAMREQUEST']._serialized_end=2620
  _globals['_CHATSTREAMRESPONSE']._serialized_start=2622
  _globals['_CHATSTREAMRESPONSE']._serialized_end=2671
# @@protoc_insertion_point(module_scope)
