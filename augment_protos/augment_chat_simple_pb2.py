# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: augment_chat_simple.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'augment_chat_simple.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x61ugment_chat_simple.proto\x12\x13\x63om.augmentcode.rpc\x1a\x1bgoogle/protobuf/empty.proto\"V\n\x16\x43hatUserMessageRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x17\n\x0f\x63onversation_id\x18\x02 \x01(\t\x12\x12\n\nrequest_id\x18\x03 \x01(\t\"Z\n\x0e\x43hatModelReply\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x12\n\nrequest_id\x18\x02 \x01(\t\x12\x11\n\tstreaming\x18\x03 \x01(\x08\x12\x13\n\x0bis_complete\x18\x04 \x01(\x08\"+\n\x15\x43hatUserCancelRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t2\xd5\x01\n\x12WebviewChatService\x12g\n\x0f\x43hatUserMessage\x12+.com.augmentcode.rpc.ChatUserMessageRequest\x1a#.com.augmentcode.rpc.ChatModelReply\"\x00\x30\x01\x12V\n\x0e\x43hatUserCancel\x12*.com.augmentcode.rpc.ChatUserCancelRequest\x1a\x16.google.protobuf.Empty\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'augment_chat_simple_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_CHATUSERMESSAGEREQUEST']._serialized_start=79
  _globals['_CHATUSERMESSAGEREQUEST']._serialized_end=165
  _globals['_CHATMODELREPLY']._serialized_start=167
  _globals['_CHATMODELREPLY']._serialized_end=257
  _globals['_CHATUSERCANCELREQUEST']._serialized_start=259
  _globals['_CHATUSERCANCELREQUEST']._serialized_end=302
  _globals['_WEBVIEWCHATSERVICE']._serialized_start=305
  _globals['_WEBVIEWCHATSERVICE']._serialized_end=518
# @@protoc_insertion_point(module_scope)
