syntax = "proto3";

package com.augmentcode.sidecar.rpc.clientInterfaces;

// This package contains proto version of client workspace request/responses.

// Add these options to generate Java classes
option java_multiple_files = true;
option java_outer_classname = "ClientWorkspacesRPCTypes";
option java_package = "com.augmentcode.sidecar.rpc.clientInterfaces";

message GetCWDResponse {
  string cwd = 1;
}

message GetWorkspaceRootResponse {
  string workspaceRoot = 1;
}

message ReadFileRequest {
  string filePath = 1;
}

message ReadFileResponse {
  string contents = 1;
  QualifiedPathName filepath = 2;
}

message QualifiedPathName {
  string rootPath = 1;
  string relPath = 2;
}

message WriteFileRequest {
  QualifiedPathName filePath = 1;
  string contents = 2;
}

message DeleteFileRequest {
  QualifiedPathName filePath = 1;
}

message GetQualifiedPathNameRequest {
  string path = 1;
}

message GetQualifiedPathNameResponse {
  optional QualifiedPathName filepath = 1;
}

message FindFilesRequest {
  string includeGlob = 1;
  string excludeGlob = 2;
  int32 maxResults = 3;
}

message FindFilesResponse {
  repeated QualifiedPathName files = 1;
}

// FileType enum matches the one in TypeScript
// Supports bitmask combinations for symlinks
enum FileType {
  UNKNOWN = 0;
  FILE = 1;
  DIRECTORY = 2;
  SYMBOLIC_LINK = 64;
  // Bitmask combinations for symlinks
  SYMBOLIC_LINK_TO_FILE = 65; // SYMBOLIC_LINK | FILE
  SYMBOLIC_LINK_TO_DIRECTORY = 66; // SYMBOLIC_LINK | DIRECTORY
}

message GetPathInfoRequest {
  string path = 1;
}

message GetPathInfoResponse {
  optional FileType type = 1;
  optional QualifiedPathName filepath = 2;
  optional bool exists = 3;
}

message ListDirectoryRequest {
  string path = 1;
  int32 depth = 2;
  bool showHidden = 3;
}

message ListDirectoryResponse {
  repeated string entries = 1;
  optional string errorMessage = 2;
}

// Rules-related messages
enum RuleType {
  ALWAYS_ATTACHED = 0;
  MANUAL = 1;
  AGENT_REQUESTED = 2;
}

message Rule {
  RuleType type = 1;
  string path = 2;
  string content = 3;
  string description = 4;
}

message LoadRulesRequest {
  bool includeGuidelines = 1;
  repeated Rule contextRules = 2;
}

message LoadRulesResponse {
  repeated Rule rules = 1;
}
