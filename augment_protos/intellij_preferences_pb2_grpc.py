# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
import intellij_preferences_pb2 as intellij__preferences__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in intellij_preferences_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class WebviewPreferencesServiceStub(object):
    """
    This service defines an abstraction of an interface that the preferences web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.PreferenceNotify = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewPreferencesService/PreferenceNotify',
                request_serializer=intellij__preferences__pb2.PreferenceNotifyRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.PreferencesLoaded = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewPreferencesService/PreferencesLoaded',
                request_serializer=intellij__preferences__pb2.PreferencesLoadedRequest.SerializeToString,
                response_deserializer=intellij__preferences__pb2.PreferencesInitializeResponse.FromString,
                _registered_method=True)
        self.PreferenceResult = channel.unary_unary(
                '/com.augmentcode.rpc.WebviewPreferencesService/PreferenceResult',
                request_serializer=intellij__preferences__pb2.PreferenceResultRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class WebviewPreferencesServiceServicer(object):
    """
    This service defines an abstraction of an interface that the preferences web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    def PreferenceNotify(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreferencesLoaded(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreferenceResult(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WebviewPreferencesServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'PreferenceNotify': grpc.unary_unary_rpc_method_handler(
                    servicer.PreferenceNotify,
                    request_deserializer=intellij__preferences__pb2.PreferenceNotifyRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'PreferencesLoaded': grpc.unary_unary_rpc_method_handler(
                    servicer.PreferencesLoaded,
                    request_deserializer=intellij__preferences__pb2.PreferencesLoadedRequest.FromString,
                    response_serializer=intellij__preferences__pb2.PreferencesInitializeResponse.SerializeToString,
            ),
            'PreferenceResult': grpc.unary_unary_rpc_method_handler(
                    servicer.PreferenceResult,
                    request_deserializer=intellij__preferences__pb2.PreferenceResultRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.augmentcode.rpc.WebviewPreferencesService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.augmentcode.rpc.WebviewPreferencesService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class WebviewPreferencesService(object):
    """
    This service defines an abstraction of an interface that the preferences web view can use.
    Web views send messages via JS objects which we parse as JSONs into the proto buffers.
    Until web views are not using HTTP as transport protocol, routing of the JS messages
    into the service calls below is managed by AugmentMessagingServiceImpl.
    """

    @staticmethod
    def PreferenceNotify(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewPreferencesService/PreferenceNotify',
            intellij__preferences__pb2.PreferenceNotifyRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreferencesLoaded(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewPreferencesService/PreferencesLoaded',
            intellij__preferences__pb2.PreferencesLoadedRequest.SerializeToString,
            intellij__preferences__pb2.PreferencesInitializeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreferenceResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.augmentcode.rpc.WebviewPreferencesService/PreferenceResult',
            intellij__preferences__pb2.PreferenceResultRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
