# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: chat_messages.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'chat_messages.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import webview_message_def_pb2 as webview__message__def__pb2
import chat_pb2 as chat__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x63hat_messages.proto\x12&com.augmentcode.common.webviews.protos\x1a\x19webview_message_def.proto\x1a\nchat.proto\"k\n\x0fSaveChatRequest\x12I\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32;.com.augmentcode.common.webviews.protos.SaveChatRequestData:\r\xb2\xe8t\tsave-chat\"\x84\x01\n\x13SaveChatRequestData\x12\x16\n\x0e\x63onversationId\x18\x01 \x01(\t\x12\x46\n\x0b\x63hatHistory\x18\x02 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\r\n\x05title\x18\x03 \x01(\tBB\n&com.augmentcode.common.webviews.protosB\x16\x43ommonWebViewChatTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'chat_messages_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.augmentcode.common.webviews.protosB\026CommonWebViewChatTypesP\001'
  _globals['_SAVECHATREQUEST']._loaded_options = None
  _globals['_SAVECHATREQUEST']._serialized_options = b'\262\350t\tsave-chat'
  _globals['_SAVECHATREQUEST']._serialized_start=102
  _globals['_SAVECHATREQUEST']._serialized_end=209
  _globals['_SAVECHATREQUESTDATA']._serialized_start=212
  _globals['_SAVECHATREQUESTDATA']._serialized_end=344
# @@protoc_insertion_point(module_scope)
