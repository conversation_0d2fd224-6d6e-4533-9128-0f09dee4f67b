# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tools.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'tools.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import chat_pb2 as chat__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0btools.proto\x12!com.augmentcode.sidecar.rpc.tools\x1a\nchat.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x91\x01\n\x12ToolsStateResponse\x12\x39\n\x04mode\x18\x01 \x01(\x0e\x32+.com.augmentcode.sidecar.rpc.tools.ChatMode\x12@\n\x05tools\x18\x02 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.tools.ToolDefinition\"\xe4\x01\n\x0eToolDefinition\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x19\n\x11input_schema_json\x18\x03 \x01(\t\x12\x42\n\x0btool_safety\x18\x04 \x01(\x0e\x32-.com.augmentcode.sidecar.rpc.tools.ToolSafety\x12\x17\n\x0fmcp_server_name\x18\x05 \x01(\t\x12\x15\n\rmcp_tool_name\x18\x06 \x01(\t\x12 \n\x18original_mcp_server_name\x18\x07 \x01(\t\"\xc9\x01\n\x0fToolCallRequest\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x11\n\ttoolUseId\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12&\n\x05input\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x42\n\x07history\x18\x05 \x03(\x0b\x32\x31.com.augmentcode.sidecar.rpc.chat.ChatHistoryItem\x12\x16\n\x0e\x63onversationId\x18\x06 \x01(\t\"\x82\x01\n\x10ToolCallResponse\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0f\n\x07isError\x18\x02 \x01(\x08\x12O\n\rcontent_nodes\x18\x03 \x03(\x0b\x32\x38.com.augmentcode.sidecar.rpc.chat.ChatRequestContentNode\"<\n\x14ToolCancelRunRequest\x12\x11\n\trequestId\x18\x01 \x01(\t\x12\x11\n\ttoolUseId\x18\x02 \x01(\t\"e\n\x1aRetrieveRemoteToolsRequest\x12G\n\x0esupportedTools\x18\x01 \x03(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\"e\n\x1bRetrieveRemoteToolsResponse\x12\x46\n\x05tools\x18\x01 \x03(\x0b\x32\x37.com.augmentcode.sidecar.rpc.tools.RemoteToolDefinition\"\xd4\x02\n\x14RemoteToolDefinition\x12I\n\x0etoolDefinition\x18\x01 \x01(\x0b\x32\x31.com.augmentcode.sidecar.rpc.tools.ToolDefinition\x12\x45\n\x0cremoteToolId\x18\x02 \x01(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\x12U\n\x12\x61vailabilityStatus\x18\x03 \x01(\x0e\x32\x39.com.augmentcode.sidecar.rpc.tools.ToolAvailabilityStatus\x12\x41\n\ntoolSafety\x18\x04 \x01(\x0e\x32-.com.augmentcode.sidecar.rpc.tools.ToolSafety\x12\x10\n\x08oauthUrl\x18\x05 \x01(\t\"\x89\x01\n\x15\x43\x61llRemoteToolRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05input\x18\x02 \x01(\t\x12?\n\x06toolId\x18\x03 \x01(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\x12\x12\n\nrequest_id\x18\x04 \x01(\t\"Z\n\x16\x43\x61llRemoteToolResponse\x12\x13\n\x0btool_output\x18\x01 \x01(\t\x12\x1b\n\x13tool_result_message\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\x05\"R\n\x15\x43hangeChatModeRequest\x12\x39\n\x04mode\x18\x01 \x01(\x0e\x32+.com.augmentcode.sidecar.rpc.tools.ChatMode\"e\n FilterToolsWithExtraInputRequest\x12\x41\n\x08tool_ids\x18\x01 \x03(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\"f\n!FilterToolsWithExtraInputResponse\x12\x41\n\x08tool_ids\x18\x01 \x03(\x0e\x32/.com.augmentcode.sidecar.rpc.tools.RemoteToolId\"9\n$GetToolStatusForSettingsPanelRequest\x12\x11\n\tuse_cache\x18\x01 \x01(\x08\"u\n%GetToolStatusForSettingsPanelResponse\x12L\n\x05tools\x18\x01 \x03(\x0b\x32=.com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings\"4\n\x0eToolIdentifier\x12\x11\n\thost_name\x18\x01 \x01(\t\x12\x0f\n\x07tool_id\x18\x02 \x01(\t\"\xbc\x02\n\x1aToolDefinitionWithSettings\x12\x45\n\ndefinition\x18\x01 \x01(\x0b\x32\x31.com.augmentcode.sidecar.rpc.tools.ToolDefinition\x12\x45\n\nidentifier\x18\x02 \x01(\x0b\x32\x31.com.augmentcode.sidecar.rpc.tools.ToolIdentifier\x12\x15\n\ris_configured\x18\x03 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x04 \x01(\x08\x12\x42\n\x0btool_safety\x18\x05 \x01(\x0e\x32-.com.augmentcode.sidecar.rpc.tools.ToolSafety\x12\x16\n\toauth_url\x18\x06 \x01(\tH\x00\x88\x01\x01\x42\x0c\n\n_oauth_url\"\x97\x03\n\x0fMcpServerConfig\x12\x14\n\x07\x63ommand\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x0c\n\x04\x61rgs\x18\x02 \x03(\t\x12\x17\n\ntimeout_ms\x18\x03 \x01(\x05H\x01\x88\x01\x01\x12H\n\x03\x65nv\x18\x04 \x03(\x0b\x32;.com.augmentcode.sidecar.rpc.tools.McpServerConfig.EnvEntry\x12$\n\x17use_shell_interpolation\x18\x05 \x01(\x08H\x02\x88\x01\x01\x12\x11\n\x04name\x18\x06 \x01(\tH\x03\x88\x01\x01\x12\x15\n\x08\x64isabled\x18\x07 \x01(\x08H\x04\x88\x01\x01\x12\x11\n\x04type\x18\x08 \x01(\tH\x05\x88\x01\x01\x12\x10\n\x03url\x18\t \x01(\tH\x06\x88\x01\x01\x1a*\n\x08\x45nvEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\n\n\x08_commandB\r\n\x0b_timeout_msB\x1a\n\x18_use_shell_interpolationB\x07\n\x05_nameB\x0b\n\t_disabledB\x07\n\x05_typeB\x06\n\x04_url\"_\n\x14SetMcpServersRequest\x12G\n\x0bmcp_servers\x18\x01 \x03(\x0b\x32\x32.com.augmentcode.sidecar.rpc.tools.McpServerConfig*-\n\nToolSafety\x12\n\n\x06UNSAFE\x10\x00\x12\x08\n\x04SAFE\x10\x01\x12\t\n\x05\x43HECK\x10\x02*\x9c\x01\n\x0cRemoteToolId\x12\x0b\n\x07Unknown\x10\x00\x12\r\n\tWebSearch\x10\x01\x12\r\n\tGitHubApi\x10\x08\x12\n\n\x06Linear\x10\x0c\x12\x08\n\x04Jira\x10\r\x12\x0e\n\nConfluence\x10\x0e\x12\n\n\x06Notion\x10\x0f\x12\x0c\n\x08Supabase\x10\x10\x12\t\n\x05Glean\x10\x11\"\x04\x08\x02\x10\x04\"\x04\x08\x05\x10\x06\"\x04\x08\x07\x10\x07\"\x04\x08\t\x10\x0b*R\n\x16ToolAvailabilityStatus\x12\x11\n\rUnknownStatus\x10\x00\x12\r\n\tAvailable\x10\x01\x12\x16\n\x12UserConfigRequired\x10\x02*\x1f\n\x08\x43hatMode\x12\x08\n\x04\x43HAT\x10\x00\x12\t\n\x05\x41GENT\x10\x01\x42\x34\n!com.augmentcode.sidecar.rpc.toolsB\rToolsRPCTypesP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tools_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.augmentcode.sidecar.rpc.toolsB\rToolsRPCTypesP\001'
  _globals['_MCPSERVERCONFIG_ENVENTRY']._loaded_options = None
  _globals['_MCPSERVERCONFIG_ENVENTRY']._serialized_options = b'8\001'
  _globals['_TOOLSAFETY']._serialized_start=3000
  _globals['_TOOLSAFETY']._serialized_end=3045
  _globals['_REMOTETOOLID']._serialized_start=3048
  _globals['_REMOTETOOLID']._serialized_end=3204
  _globals['_TOOLAVAILABILITYSTATUS']._serialized_start=3206
  _globals['_TOOLAVAILABILITYSTATUS']._serialized_end=3288
  _globals['_CHATMODE']._serialized_start=3290
  _globals['_CHATMODE']._serialized_end=3321
  _globals['_TOOLSSTATERESPONSE']._serialized_start=93
  _globals['_TOOLSSTATERESPONSE']._serialized_end=238
  _globals['_TOOLDEFINITION']._serialized_start=241
  _globals['_TOOLDEFINITION']._serialized_end=469
  _globals['_TOOLCALLREQUEST']._serialized_start=472
  _globals['_TOOLCALLREQUEST']._serialized_end=673
  _globals['_TOOLCALLRESPONSE']._serialized_start=676
  _globals['_TOOLCALLRESPONSE']._serialized_end=806
  _globals['_TOOLCANCELRUNREQUEST']._serialized_start=808
  _globals['_TOOLCANCELRUNREQUEST']._serialized_end=868
  _globals['_RETRIEVEREMOTETOOLSREQUEST']._serialized_start=870
  _globals['_RETRIEVEREMOTETOOLSREQUEST']._serialized_end=971
  _globals['_RETRIEVEREMOTETOOLSRESPONSE']._serialized_start=973
  _globals['_RETRIEVEREMOTETOOLSRESPONSE']._serialized_end=1074
  _globals['_REMOTETOOLDEFINITION']._serialized_start=1077
  _globals['_REMOTETOOLDEFINITION']._serialized_end=1417
  _globals['_CALLREMOTETOOLREQUEST']._serialized_start=1420
  _globals['_CALLREMOTETOOLREQUEST']._serialized_end=1557
  _globals['_CALLREMOTETOOLRESPONSE']._serialized_start=1559
  _globals['_CALLREMOTETOOLRESPONSE']._serialized_end=1649
  _globals['_CHANGECHATMODEREQUEST']._serialized_start=1651
  _globals['_CHANGECHATMODEREQUEST']._serialized_end=1733
  _globals['_FILTERTOOLSWITHEXTRAINPUTREQUEST']._serialized_start=1735
  _globals['_FILTERTOOLSWITHEXTRAINPUTREQUEST']._serialized_end=1836
  _globals['_FILTERTOOLSWITHEXTRAINPUTRESPONSE']._serialized_start=1838
  _globals['_FILTERTOOLSWITHEXTRAINPUTRESPONSE']._serialized_end=1940
  _globals['_GETTOOLSTATUSFORSETTINGSPANELREQUEST']._serialized_start=1942
  _globals['_GETTOOLSTATUSFORSETTINGSPANELREQUEST']._serialized_end=1999
  _globals['_GETTOOLSTATUSFORSETTINGSPANELRESPONSE']._serialized_start=2001
  _globals['_GETTOOLSTATUSFORSETTINGSPANELRESPONSE']._serialized_end=2118
  _globals['_TOOLIDENTIFIER']._serialized_start=2120
  _globals['_TOOLIDENTIFIER']._serialized_end=2172
  _globals['_TOOLDEFINITIONWITHSETTINGS']._serialized_start=2175
  _globals['_TOOLDEFINITIONWITHSETTINGS']._serialized_end=2491
  _globals['_MCPSERVERCONFIG']._serialized_start=2494
  _globals['_MCPSERVERCONFIG']._serialized_end=2901
  _globals['_MCPSERVERCONFIG_ENVENTRY']._serialized_start=2765
  _globals['_MCPSERVERCONFIG_ENVENTRY']._serialized_end=2807
  _globals['_SETMCPSERVERSREQUEST']._serialized_start=2903
  _globals['_SETMCPSERVERSREQUEST']._serialized_end=2998
# @@protoc_insertion_point(module_scope)
