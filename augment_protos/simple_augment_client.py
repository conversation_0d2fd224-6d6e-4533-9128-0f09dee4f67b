"""
简化版Augment gRPC API客户端
使用简化的protobuf定义，避免依赖冲突
"""

import grpc
import sys
import os
import time
import logging
import json
from typing import Dict, Any, Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入简化版protobuf模块
try:
    import augment_chat_simple_pb2
    import augment_chat_simple_pb2_grpc
    print("✅ 简化版protobuf模块导入成功")
except ImportError as e:
    print(f"❌ 导入protobuf模块失败: {e}")
    sys.exit(1)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleAugmentClient:
    """
    简化版Augment API客户端
    """
    
    def __init__(self, host: str = "localhost", port: int = None):
        self.host = host
        self.port = port
        self.channel = None
        self.stub = None
        
    def find_augment_service(self) -> Optional[int]:
        """
        查找Augment服务端口
        """
        # 常见的gRPC端口
        common_ports = [50051, 50052, 8080, 8081, 9090, 9091, 3000, 3001, 5000, 5001]
        
        logger.info("正在搜索Augment gRPC服务...")
        
        for port in common_ports:
            logger.info(f"测试端口: {port}")
            if self.test_connection(self.host, port):
                logger.info(f"✅ 发现Augment服务在端口: {port}")
                return port
        
        logger.warning("❌ 未找到Augment gRPC服务")
        return None
    
    def test_connection(self, host: str, port: int) -> bool:
        """
        测试TCP连接
        """
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception as e:
            logger.debug(f"连接测试失败 {host}:{port} - {e}")
            return False
    
    def connect(self, port: int = None) -> bool:
        """
        连接到Augment服务
        """
        try:
            # 使用指定端口或查找服务
            if port:
                self.port = port
            elif not self.port:
                found_port = self.find_augment_service()
                if found_port:
                    self.port = found_port
                else:
                    logger.error("无法找到Augment服务")
                    return False
            
            address = f"{self.host}:{self.port}"
            logger.info(f"连接到Augment服务: {address}")
            
            # 创建gRPC通道
            self.channel = grpc.insecure_channel(address)
            
            # 创建服务存根
            self.stub = augment_chat_simple_pb2_grpc.WebviewChatServiceStub(self.channel)
            
            # 测试连接
            grpc.channel_ready_future(self.channel).result(timeout=5)
            logger.info("✅ gRPC连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ gRPC连接失败: {e}")
            return False
    
    def send_chat_message(self, message: str, conversation_id: str = None) -> Dict[str, Any]:
        """
        发送聊天消息
        """
        if not self.channel or not self.stub:
            logger.error("未连接到服务，请先调用connect()")
            return {"error": "未连接到服务"}
        
        try:
            # 构造请求
            request = augment_chat_simple_pb2.ChatUserMessageRequest()
            request.message = message
            request.conversation_id = conversation_id or f"conv_{int(time.time())}"
            request.request_id = f"req_{int(time.time() * 1000)}"
            
            logger.info(f"发送聊天消息: {message}")
            logger.info(f"会话ID: {request.conversation_id}")
            logger.info(f"请求ID: {request.request_id}")
            
            # 发送请求并接收流式响应
            response_stream = self.stub.ChatUserMessage(request)
            
            responses = []
            full_text = ""
            
            for response in response_stream:
                response_data = {
                    "text": response.text,
                    "request_id": response.request_id,
                    "streaming": response.streaming,
                    "is_complete": response.is_complete
                }
                responses.append(response_data)
                full_text += response.text
                
                logger.info(f"收到响应片段: {response.text}")
                
                if response.is_complete:
                    logger.info("响应完成")
                    break
            
            return {
                "success": True,
                "responses": responses,
                "full_text": full_text,
                "conversation_id": request.conversation_id,
                "request_id": request.request_id
            }
            
        except grpc.RpcError as e:
            error_msg = f"gRPC错误: {e.code()} - {e.details()}"
            logger.error(error_msg)
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"发送消息失败: {e}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def cancel_chat(self, request_id: str) -> bool:
        """
        取消聊天请求
        """
        try:
            if not self.stub:
                return False
                
            request = augment_chat_simple_pb2.ChatUserCancelRequest()
            request.request_id = request_id
            
            self.stub.ChatUserCancel(request)
            logger.info(f"已取消聊天请求: {request_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消聊天失败: {e}")
            return False
    
    def close(self):
        """
        关闭连接
        """
        if self.channel:
            self.channel.close()
            logger.info("gRPC连接已关闭")

def test_client():
    """
    测试客户端功能
    """
    print("=== 简化版Augment API客户端测试 ===")
    
    client = SimpleAugmentClient()
    
    # 测试连接
    print("\n1. 测试连接...")
    if not client.connect():
        print("❌ 连接失败")
        print("\n可能的原因:")
        print("- Augment插件未运行")
        print("- IntelliJ IDEA未启动")
        print("- gRPC服务未启动")
        print("- 端口被占用或防火墙阻止")
        return
    
    # 测试发送消息
    print("\n2. 测试发送消息...")
    test_message = "Hello Augment! 请帮我分析一下这个项目的代码结构。"
    
    response = client.send_chat_message(test_message)
    
    if response.get("success"):
        print("✅ 消息发送成功")
        print(f"完整响应: {response['full_text']}")
        print(f"响应片段数: {len(response['responses'])}")
    else:
        print(f"❌ 消息发送失败: {response.get('error')}")
    
    # 关闭连接
    client.close()
    print("\n🎉 测试完成")

def interactive_chat():
    """
    交互式聊天模式
    """
    print("=== Augment 交互式聊天 ===")
    print("输入消息与Augment对话，输入 'quit' 退出")
    
    client = SimpleAugmentClient()
    
    # 尝试连接
    if not client.connect():
        print("❌ 无法连接到Augment服务")
        print("请确保:")
        print("1. IntelliJ IDEA已启动")
        print("2. Augment插件已加载")
        print("3. 没有防火墙阻止连接")
        return
    
    conversation_id = f"interactive_{int(time.time())}"
    print(f"会话ID: {conversation_id}")
    print("-" * 50)
    
    try:
        while True:
            # 获取用户输入
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q', '退出']:
                break
            
            if not user_input:
                continue
            
            print("Augment: ", end="", flush=True)
            
            # 发送消息
            response = client.send_chat_message(user_input, conversation_id)
            
            if response.get("success"):
                # 实时显示响应
                for resp in response["responses"]:
                    print(resp["text"], end="", flush=True)
                print()  # 换行
            else:
                print(f"\n❌ 错误: {response.get('error')}")
                
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出聊天")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    finally:
        client.close()
        print("连接已关闭")

def manual_test():
    """
    手动测试特定端口
    """
    print("=== 手动测试特定端口 ===")
    
    try:
        port = int(input("请输入要测试的端口号: "))
    except ValueError:
        print("无效的端口号")
        return
    
    client = SimpleAugmentClient()
    
    if client.connect(port):
        print(f"✅ 成功连接到端口 {port}")
        
        # 发送测试消息
        test_message = input("请输入测试消息: ").strip()
        if test_message:
            response = client.send_chat_message(test_message)
            
            if response.get("success"):
                print("✅ 消息发送成功")
                print("响应:", response["full_text"])
            else:
                print("❌ 消息发送失败:", response.get("error"))
    else:
        print(f"❌ 无法连接到端口 {port}")
    
    client.close()

def main():
    """
    主函数
    """
    print("=== 简化版Augment gRPC API客户端 ===")
    print("1. 自动测试连接")
    print("2. 交互式聊天")
    print("3. 手动测试端口")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        test_client()
    elif choice == "2":
        interactive_chat()
    elif choice == "3":
        manual_test()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
