请详细分析这个unknown项目 "yard-entrance-management" 的业务流程和技术架构，并将分析结果以JSON格式保存到文件中。

**项目基本信息：**
- 项目名称: yard-entrance-management
- 项目类型: unknown
- 文件数量: 40223
- 主要文件类型: .jar, .ps1, .xml, .md, .local

**重要要求：**
1. 请将所有分析结果组织成结构化的JSON格式
2. **请使用save-file工具将JSON数据保存到文件 `yard-entrance-management_analysis_report.json` 中**
3. 请特别关注这个unknown的特有架构和模式
4. JSON格式应包含以下字段结构：
   - project_analysis (项目分析主体)
     - project_overview (项目概述)
     - technical_architecture (技术架构)
     - business_process (业务流程)
     - feature_assessment (功能评估)
     - code_quality (代码质量)
     - improvement_suggestions (改进建议)
     - technical_debt_risks (技术债务风险)
   - analysis_metadata (分析元数据)

请基于项目的实际代码和结构，提供详细、专业的JSON格式分析报告。

**执行步骤：**
1. 使用codebase-retrieval工具深度分析项目代码
2. 分析项目的各个方面（架构、业务流程、代码质量等）
3. 将分析结果组织成完整的JSON格式
4. **使用save-file工具将完整的JSON数据保存到 `yard-entrance-management_analysis_report.json` 文件中**

**重要提醒：请务必使用save-file工具创建JSON文件，不要只是显示JSON内容。**