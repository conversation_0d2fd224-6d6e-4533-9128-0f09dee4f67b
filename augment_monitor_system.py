"""
Augment监听系统 - 持续监听Augment响应并保存到数据库
"""

import time
import pyautogui
import pyperclip
import json
import sqlite3
import threading
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志 - 修复编码问题
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AugmentMonitorSystem:
    """
    Augment监听系统 - 自动业务分析版本
    """

    def __init__(self, input_position: tuple = (1800, 900)):
        self.input_position = input_position
        self.is_running = False
        self.db_path = "augment_responses.db"
        self.last_response = ""
        self.response_positions = [
            # 基于输入框位置的响应搜索区域
            (input_position[0], input_position[1] - 100),
            (input_position[0], input_position[1] - 150),
            (input_position[0], input_position[1] - 200),
            (input_position[0], input_position[1] - 250),
            (input_position[0] - 100, input_position[1] - 150),
            (input_position[0] - 200, input_position[1] - 200),
            (input_position[0] - 300, input_position[1] - 250),
        ]

        # 固定的业务分析提示词
        self.business_analysis_prompt = self.get_business_analysis_prompt()

        # 初始化数据库
        self.init_database()

        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.3

    def get_business_analysis_prompt(self) -> str:
        """
        获取固定的业务分析提示词 - 要求Augment自己写入JSON文件
        """
        return """请详细分析这个AI辅助项目的业务流程和技术架构，并将分析结果以JSON格式保存到文件中。

**重要要求：**
1. 请将所有分析结果组织成结构化的JSON格式
2. **请使用save-file工具将JSON数据保存到文件 `business_analysis_report.json` 中**
3. JSON格式应包含以下字段结构：

```json
{
  "project_analysis": {
    "project_overview": {
      "main_purpose": "项目的主要目的和功能",
      "business_value": "核心业务价值和应用场景",
      "target_users": "目标用户群体"
    },
    "technical_architecture": {
      "code_structure": "代码结构和模块组织",
      "tech_stack": "主要技术栈和依赖",
      "design_patterns": "系统架构设计模式",
      "data_flow": "数据流和处理逻辑"
    },
    "business_process": {
      "core_workflow": "核心业务流程步骤",
      "user_interaction": "用户交互流程",
      "data_processing": "数据处理和存储流程",
      "error_handling": "异常处理机制"
    },
    "feature_assessment": {
      "implemented_features": "已实现的核心功能",
      "system_advantages": "系统的优势和特点",
      "performance_analysis": "性能和可扩展性分析"
    },
    "code_quality": {
      "organization": "代码组织和可维护性",
      "error_handling": "错误处理和日志记录",
      "security": "安全性考虑",
      "test_coverage": "测试覆盖情况"
    },
    "improvement_suggestions": {
      "architecture_optimization": "架构优化建议",
      "feature_enhancement": "功能增强建议",
      "performance_optimization": "性能优化方向",
      "security_improvement": "安全性改进",
      "user_experience": "用户体验提升"
    },
    "technical_debt_risks": {
      "potential_risks": "潜在的技术风险",
      "refactoring_needs": "需要重构的部分",
      "dependency_issues": "依赖管理问题",
      "compatibility_concerns": "兼容性考虑"
    }
  },
  "analysis_metadata": {
    "analysis_date": "分析日期",
    "analysis_scope": "分析范围",
    "confidence_level": "分析可信度"
  }
}
```

请基于项目的实际代码和结构，提供详细、专业的JSON格式分析报告。

**执行步骤：**
1. 分析项目的各个方面（架构、业务流程、代码质量等）
2. 将分析结果组织成上述JSON格式
3. **使用save-file工具将完整的JSON数据保存到 `business_analysis_report.json` 文件中**
4. 确保保存的JSON文件格式正确，可以被程序解析

**重要提醒：请务必使用save-file工具创建JSON文件，不要只是显示JSON内容。**"""
    
    def init_database(self):
        """
        初始化数据库
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建响应表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS augment_responses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    request_message TEXT NOT NULL,
                    response_content TEXT NOT NULL,
                    response_length INTEGER NOT NULL,
                    quality_score INTEGER NOT NULL,
                    input_position TEXT NOT NULL,
                    response_position TEXT,
                    json_data TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS augment_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    end_time DATETIME,
                    total_requests INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active'
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
    
    def send_message_to_augment(self, message: str) -> bool:
        """
        发送消息到Augment
        """
        try:
            logger.info(f"📝 发送消息到Augment: {message}")
            
            # 复制消息到剪贴板
            pyperclip.copy(message)
            
            # 点击输入框
            pyautogui.click(self.input_position[0], self.input_position[1])
            time.sleep(1)
            
            # 清空并粘贴
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.3)
            pyautogui.press('delete')
            time.sleep(0.3)
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(1)
            
            # 验证输入
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.3)
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(0.5)
            
            verification = pyperclip.paste()
            
            if verification and message in verification:
                # 发送消息
                pyautogui.press('enter')
                time.sleep(2)
                logger.info("✅ 消息发送成功")
                return True
            else:
                logger.error("❌ 消息输入验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 发送消息失败: {e}")
            return False
    
    def monitor_response(self, timeout: int = 120) -> Optional[Dict[str, Any]]:
        """
        监听Augment响应
        """
        logger.info(f"⏳ 开始监听响应 (超时: {timeout}秒)")
        
        start_time = time.time()
        best_response = ""
        best_position = None
        
        while time.time() - start_time < timeout:
            try:
                # 检查每个响应位置
                for pos in self.response_positions:
                    try:
                        # 点击响应区域
                        pyautogui.click(pos[0], pos[1])
                        time.sleep(0.3)
                        
                        # 全选并复制
                        pyautogui.hotkey('ctrl', 'a')
                        time.sleep(0.3)
                        pyautogui.hotkey('ctrl', 'c')
                        time.sleep(0.5)
                        
                        content = pyperclip.paste()
                        
                        if content and len(content) > 100:
                            # 检查是否是新的响应
                            if content != self.last_response and len(content) > len(best_response):
                                # 计算质量分数
                                quality_score = self.calculate_quality_score(content)
                                
                                if quality_score >= 3:  # 质量阈值
                                    best_response = content
                                    best_position = pos
                                    logger.info(f"🎯 发现新响应: {len(content)} 字符, 质量: {quality_score}")
                    
                    except Exception as e:
                        logger.debug(f"检查响应位置 {pos} 失败: {e}")
                        continue
                
                # 如果找到了好的响应，返回
                if best_response and len(best_response) > len(self.last_response):
                    self.last_response = best_response
                    
                    response_data = {
                        "content": best_response,
                        "length": len(best_response),
                        "position": best_position,
                        "quality_score": self.calculate_quality_score(best_response),
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    logger.info("✅ 响应监听完成")
                    return response_data
                
                # 等待一段时间再检查
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"监听响应时出错: {e}")
                time.sleep(2)
        
        logger.warning("⚠️ 响应监听超时")
        return None
    
    def calculate_quality_score(self, content: str) -> int:
        """
        计算响应质量分数
        """
        quality_keywords = [
            '分析', '项目', 'ai', '代码', '结构', '功能', '建议',
            'python', '技术', '架构', '模块', '组件', '优化',
            '改进', '质量', '性能', '安全', '维护', '测试'
        ]
        
        score = sum(1 for keyword in quality_keywords if keyword.lower() in content.lower())
        return score

    def is_json_content(self, content: str) -> bool:
        """
        检测内容是否为JSON格式
        """
        try:
            # 尝试查找JSON代码块
            import re
            json_pattern = r'```json\s*(.*?)\s*```'
            json_match = re.search(json_pattern, content, re.DOTALL | re.IGNORECASE)

            if json_match:
                json_str = json_match.group(1).strip()
                json.loads(json_str)
                return True

            # 尝试直接解析整个内容
            json.loads(content.strip())
            return True

        except (json.JSONDecodeError, AttributeError):
            # 检查是否包含JSON关键字
            json_indicators = ['{', '}', '"project_analysis"', '"analysis_metadata"',
                             'json', 'JSON', '"main_purpose"', '"business_value"']
            return any(indicator in content for indicator in json_indicators)

        return False
    
    def save_to_database(self, request_message: str, response_data: Dict[str, Any]) -> int:
        """
        保存到数据库 - 增强业务分析格式
        """
        try:
            # 创建专门针对JSON输出的数据结构
            json_data = {
                "analysis_type": "business_process_analysis_json_output",
                "project_info": {
                    "name": "AI-assisted Project",
                    "analysis_date": datetime.now().isoformat(),
                    "workspace_path": "D:/project/AI-assisted",
                    "output_format": "structured_json"
                },
                "request": {
                    "message": request_message,
                    "timestamp": datetime.now().isoformat(),
                    "input_position": self.input_position,
                    "analysis_scope": "complete_business_analysis_json",
                    "expected_output": "structured_json_format"
                },
                "augment_response": {
                    "raw_content": response_data["content"],
                    "content_length": response_data["length"],
                    "quality_score": response_data["quality_score"],
                    "timestamp": response_data["timestamp"],
                    "position": response_data["position"],
                    "is_json_format": self.is_json_content(response_data["content"]),
                    "analysis_categories": [
                        "project_overview",
                        "technical_architecture",
                        "business_process",
                        "feature_assessment",
                        "code_quality",
                        "improvement_suggestions",
                        "technical_debt_risks"
                    ]
                },
                "processing_metadata": {
                    "system": "AugmentMonitorSystem",
                    "version": "2.0_json_business_analysis",
                    "processing_time": time.time(),
                    "analysis_mode": "automated_json_business_analysis",
                    "json_extraction_attempted": True
                }
            }
            
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO augment_responses 
                (timestamp, request_message, response_content, response_length, 
                 quality_score, input_position, response_position, json_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                request_message,
                response_data["content"],
                response_data["length"],
                response_data["quality_score"],
                str(self.input_position),
                str(response_data["position"]),
                json.dumps(json_data, ensure_ascii=False, indent=2)
            ))
            
            record_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # 检查Augment是否创建了JSON文件
            import os
            expected_json_file = "business_analysis_report.json"
            current_dir = os.getcwd()
            json_filepath = os.path.join(current_dir, expected_json_file)

            # 记录系统响应到数据库（包含监听到的内容）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            system_log_filename = f"system_response_{timestamp}.json"
            system_log_filepath = os.path.join(current_dir, system_log_filename)

            with open(system_log_filepath, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            # 检查Augment是否创建了预期的JSON文件
            augment_created_file = os.path.exists(json_filepath)

            if augment_created_file:
                logger.info(f"✅ 业务分析完成！Augment已创建JSON文件: {json_filepath}")
                logger.info(f"✅ 系统响应已保存到数据库 (ID: {record_id}) 和系统日志 ({system_log_filepath})")
            else:
                logger.info(f"✅ 系统响应已保存到数据库 (ID: {record_id}) 和系统日志 ({system_log_filepath})")
                logger.warning(f"⚠️ 未检测到Augment创建的JSON文件: {json_filepath}")

            return record_id
            
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
            return -1
    
    def process_request(self, message: str) -> Dict[str, Any]:
        """
        处理单个请求的完整流程
        """
        logger.info(f"🚀 开始处理请求: {message}")
        
        result = {
            "success": False,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "error": None,
            "database_id": None,
            "json_file": None
        }
        
        try:
            # 1. 发送消息
            if not self.send_message_to_augment(message):
                result["error"] = "消息发送失败"
                return result
            
            # 2. 监听响应
            response_data = self.monitor_response()
            if not response_data:
                result["error"] = "未获取到响应"
                return result
            
            # 3. 保存到数据库
            db_id = self.save_to_database(message, response_data)
            if db_id > 0:
                result["success"] = True
                result["database_id"] = db_id
                result["response_length"] = response_data["length"]
                result["quality_score"] = response_data["quality_score"]
                
                logger.info(f"🎉 请求处理完成: DB ID {db_id}")
            else:
                result["error"] = "数据保存失败"
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ 处理请求失败: {e}")
        
        return result

    def run_business_analysis(self) -> Dict[str, Any]:
        """
        运行自动业务分析 - JSON格式输出
        """
        logger.info("🚀 开始自动业务分析 - JSON格式输出")
        print("=== AI辅助项目自动业务分析系统 (JSON输出版) ===")
        print("正在启动业务分析...")
        print("分析范围：项目架构、业务流程、技术特性、改进建议")
        print("输出格式：结构化JSON数据")
        print("特别要求：Augment将分析结果输出为JSON格式")
        print("-" * 60)

        try:
            # 使用固定的业务分析提示词
            result = self.process_request(self.business_analysis_prompt)

            if result["success"]:
                print("✅ 业务分析请求已发送！")
                print(f"📊 Augment正在生成结构化JSON分析报告")
                print(f"   数据库ID: {result['database_id']}")
                print(f"   响应长度: {result['response_length']} 字符")
                print(f"   质量评分: {result['quality_score']}")
                print(f"   预期JSON文件: business_analysis_report.json")
                print("\n🎯 Augment将自动创建包含以下内容的JSON文件：")
                print("   ✓ 项目概述和价值分析")
                print("   ✓ 技术架构深度解析")
                print("   ✓ 业务流程详细梳理")
                print("   ✓ 功能特性全面评估")
                print("   ✓ 代码质量专业分析")
                print("   ✓ 改进建议和优化方向")
                print("   ✓ 技术债务和风险识别")
                print("\n📋 特别说明：已要求Augment使用save-file工具创建JSON文件")
            else:
                print(f"❌ 业务分析失败: {result['error']}")
                logger.error(f"业务分析失败: {result['error']}")

            return result

        except Exception as e:
            error_msg = f"业务分析过程出错: {e}"
            logger.error(error_msg)
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }

    def start_continuous_monitoring(self):
        """
        启动持续监听模式
        """
        logger.info("🔄 启动持续监听模式")
        self.is_running = True
        
        print("=== Augment持续监听系统 ===")
        print("系统已启动，等待用户输入...")
        print("输入 'quit' 退出系统")
        print("-" * 50)
        
        while self.is_running:
            try:
                # 获取用户输入
                user_input = input("\n请输入要发送给Augment的消息 (或输入 'quit' 退出): ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    self.is_running = False
                    break
                
                if not user_input:
                    continue
                
                # 处理请求
                result = self.process_request(user_input)
                
                # 显示结果
                if result["success"]:
                    print(f"✅ 处理成功!")
                    print(f"   数据库ID: {result['database_id']}")
                    print(f"   响应长度: {result['response_length']} 字符")
                    print(f"   质量评分: {result['quality_score']}")
                else:
                    print(f"❌ 处理失败: {result['error']}")
                
            except KeyboardInterrupt:
                print("\n\n⚠️ 用户中断，正在退出...")
                self.is_running = False
                break
            except Exception as e:
                logger.error(f"持续监听出错: {e}")
                time.sleep(2)
        
        logger.info("🛑 持续监听系统已停止")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取总记录数
            cursor.execute("SELECT COUNT(*) FROM augment_responses")
            total_records = cursor.fetchone()[0]
            
            # 获取今天的记录数
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute("SELECT COUNT(*) FROM augment_responses WHERE DATE(timestamp) = ?", (today,))
            today_records = cursor.fetchone()[0]
            
            # 获取平均质量分数
            cursor.execute("SELECT AVG(quality_score) FROM augment_responses")
            avg_quality = cursor.fetchone()[0] or 0
            
            conn.close()
            
            return {
                "total_records": total_records,
                "today_records": today_records,
                "average_quality": round(avg_quality, 2),
                "database_path": self.db_path
            }
            
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}

def main():
    """
    主函数 - 自动业务分析模式 (JSON格式输出)
    """
    print("=== AI辅助项目自动业务分析系统 (Augment自动保存JSON版) ===")
    print("系统将自动执行项目业务分析，无需手动输入")
    print("分析内容：架构设计、业务流程、技术特性、优化建议")
    print("输出格式：Augment将自动创建 business_analysis_report.json 文件")
    print("特殊功能：要求Augment使用save-file工具保存JSON数据")
    print("=" * 60)

    try:
        # 直接创建监听系统并运行业务分析
        monitor = AugmentMonitorSystem(input_position=(1800, 900))

        # 显示系统信息
        print(f"📍 输入位置: {monitor.input_position}")
        print(f"🗄️  数据库路径: {monitor.db_path}")
        print(f"📝 分析提示词长度: {len(monitor.business_analysis_prompt)} 字符")

        # 运行业务分析
        result = monitor.run_business_analysis()

        # 显示最终结果
        print("\n" + "=" * 60)
        if result["success"]:
            print("🎉 业务分析系统执行完成！")
            print("📋 分析报告已生成并保存到JSON文件")
            print("💾 数据已同步保存到数据库")
        else:
            print("⚠️ 业务分析执行遇到问题")
            print(f"错误信息: {result.get('error', '未知错误')}")

        # 显示数据库统计
        print("\n📊 数据库统计信息:")
        stats = monitor.get_database_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")

    except Exception as e:
        print(f"❌ 系统执行失败: {e}")
        logger.error(f"主函数执行失败: {e}")

    print("\n👋 业务分析系统已结束")

if __name__ == "__main__":
    main()
