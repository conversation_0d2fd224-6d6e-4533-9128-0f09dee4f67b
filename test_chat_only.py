"""
直接测试聊天输入功能
"""

import time
import pyautogui
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_chat_input():
    """
    测试聊天输入功能 - 直接版本
    """
    print("=== 测试Augment聊天输入 ===")
    
    try:
        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        print("✅ 初始化完成")
        
        # 确认状态
        print("🔍 当前状态确认:")
        print("- Augment聊天面板已打开")
        print("- 光标已在输入框中")
        print("- 准备发送分析请求")
        
        input("请确认Augment聊天界面已打开且光标在输入框中，然后按Enter继续...")
        
        # 构造分析请求
        analysis_message = """请分析这个AI辅助项目的代码结构，包括：

🏗️ **项目架构分析**
- 整体架构设计
- 模块组织和依赖关系
- 设计模式使用情况

📊 **代码质量评估**
- 代码规范性检查
- 可维护性分析
- 潜在问题识别

🔧 **技术栈分析**
- 使用的编程语言和框架
- 第三方库依赖分析
- 技术选型合理性

⚡ **性能和优化**
- 性能瓶颈识别
- 资源使用分析
- 优化建议

🔒 **安全性审查**
- 安全漏洞检查
- 数据处理安全性
- 安全最佳实践

📈 **改进建议**
- 代码重构建议
- 功能扩展方向
- 开发最佳实践

请提供详细的分析报告，包含具体的代码示例和实用的改进方案。"""
        
        # 复制到剪贴板
        pyperclip.copy(analysis_message)
        print("✅ 分析请求已复制到剪贴板")
        
        print("\n📝 发送分析请求...")
        
        # 方法1: 直接粘贴
        try:
            print("🎯 方法1: 直接粘贴")
            
            # 清空输入框
            print("🧹 清空输入框...")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)
            pyautogui.press('delete')
            time.sleep(0.5)
            
            # 粘贴内容
            print("📋 粘贴分析请求...")
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(2)
            
            # 发送消息
            print("🚀 发送消息...")
            pyautogui.press('enter')
            time.sleep(2)
            
            print("✅ 分析请求已发送")
            
            # 确认发送成功
            response = input("你能在Augment界面中看到这条分析请求吗？(y/n): ").strip().lower()
            
            if response == 'y':
                print("✅ 消息发送成功")
                
                # 等待分析完成
                wait_for_analysis()
                
                # 获取结果
                result = get_analysis_result()
                
                if result:
                    save_result(result)
                    print("🎉 测试完成！")
                    return True
                else:
                    print("⚠️ 未能获取分析结果，但发送成功")
                    return True
            else:
                print("❌ 消息发送失败，尝试手动方法")
                return manual_send_method(analysis_message)
                
        except Exception as e:
            print(f"❌ 自动发送失败: {e}")
            return manual_send_method(analysis_message)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def manual_send_method(message):
    """
    手动发送方法
    """
    print("\n🎯 手动发送方法")
    print("自动发送失败，请手动操作:")
    print("1. 确认光标在Augment输入框中")
    print("2. 按 Ctrl+V 粘贴分析请求")
    print("3. 按 Enter 发送消息")
    print()
    print("分析请求内容已在剪贴板中，可以直接粘贴")
    
    input("手动发送完成后，按Enter继续...")
    
    # 等待分析
    wait_for_analysis()
    
    # 获取结果
    result = get_analysis_result()
    
    if result:
        save_result(result)
        print("🎉 手动测试完成！")
        return True
    else:
        print("⚠️ 未能获取分析结果")
        return True

def wait_for_analysis():
    """
    等待分析完成
    """
    print("\n⏳ 等待Augment分析完成...")
    print("观察Augment界面，等待分析结果出现")
    
    # 分阶段等待
    wait_stages = [
        (60, "初始等待"),
        (60, "继续等待"),
        (60, "最后等待")
    ]
    
    for stage_time, stage_name in wait_stages:
        print(f"\n⏰ {stage_name} ({stage_time}秒)")
        
        # 倒计时显示
        for remaining in range(stage_time, 0, -15):
            print(f"   剩余 {remaining} 秒...", end='\r')
            time.sleep(15)
        
        print()  # 换行
        
        # 询问用户
        try:
            response = input("分析完成了吗？(y=完成, n=继续等待, Enter=继续等待): ").strip().lower()
            if response == 'y':
                print("✅ 用户确认分析完成")
                return
            elif response == 'n':
                print("⏳ 继续等待...")
                continue
            else:
                print("⏳ 继续等待...")
        except:
            print("⏳ 继续等待...")
    
    print("✅ 等待阶段完成")

def get_analysis_result():
    """
    获取分析结果
    """
    try:
        print("\n📋 获取分析结果...")
        
        # 方法1: 全选复制
        print("🎯 尝试全选复制...")
        
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)
        
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)
        
        result = pyperclip.paste()
        
        if result and len(result) > 100:
            print(f"✅ 自动获取成功 ({len(result)} 字符)")
            return result
        else:
            print("❌ 自动获取失败或内容太短")
            
            # 方法2: 手动复制
            print("\n🎯 手动复制方法:")
            print("请手动操作:")
            print("1. 选择Augment的分析结果文本")
            print("2. 按 Ctrl+C 复制")
            print("3. 然后按Enter继续")
            
            input("复制完成后按Enter...")
            
            result = pyperclip.paste()
            
            if result and len(result) > 50:
                print(f"✅ 手动获取成功 ({len(result)} 字符)")
                return result
            else:
                print("❌ 仍未获取到有效结果")
                return None
                
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_result(result):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        filename = f"augment_chat_test_result_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment聊天测试结果 ===\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目: AI-assisted\n")
            f.write(f"测试方法: 直接聊天输入\n")
            f.write("=" * 60 + "\n\n")
            f.write(result)
        
        print(f"📁 分析结果已保存到: {filename}")
        
        # 显示预览
        print("\n📄 分析结果预览:")
        print("-" * 50)
        preview = result[:500] + "..." if len(result) > 500 else result
        print(preview)
        print("-" * 50)
        
        # 保存截图
        try:
            screenshot_file = f"augment_chat_test_screenshot_{timestamp}.png"
            pyautogui.screenshot().save(screenshot_file)
            print(f"📸 截图已保存: {screenshot_file}")
        except Exception as e:
            print(f"⚠️ 截图保存失败: {e}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def quick_test():
    """
    快速测试 - 发送简单消息
    """
    print("=== 快速聊天测试 ===")
    
    try:
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        print("准备发送简单测试消息...")
        input("确认Augment聊天界面已打开且光标在输入框中，按Enter继续...")
        
        # 简单测试消息
        test_message = "Hello Augment! 请简单介绍一下这个AI辅助项目的主要功能和特点。"
        
        # 复制到剪贴板
        pyperclip.copy(test_message)
        print("✅ 测试消息已复制到剪贴板")
        
        # 发送消息
        print("📝 发送测试消息...")
        
        # 清空并粘贴
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(1)
        
        # 发送
        pyautogui.press('enter')
        time.sleep(2)
        
        print("✅ 测试消息已发送")
        
        # 确认
        response = input("你能看到Augment的响应吗？(y/n): ").strip().lower()
        
        if response == 'y':
            print("✅ 快速测试成功")
            
            # 简单获取结果
            input("等待响应完成后按Enter...")
            
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(1)
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(2)
            
            result = pyperclip.paste()
            
            if result and len(result) > 20:
                print(f"✅ 获取到响应 ({len(result)} 字符)")
                
                # 保存简单结果
                timestamp = int(time.time())
                filename = f"augment_quick_test_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("=== Augment快速测试结果 ===\n")
                    f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 40 + "\n\n")
                    f.write(result)
                
                print(f"📁 结果已保存: {filename}")
                
                print("\n响应预览:")
                print("-" * 30)
                print(result[:300] + "..." if len(result) > 300 else result)
                print("-" * 30)
                
                return True
            else:
                print("❌ 未获取到有效响应")
                return False
        else:
            print("❌ 快速测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试异常: {e}")
        return False

def main():
    """
    主函数
    """
    print("=== Augment聊天输入测试工具 ===")
    print("专门测试聊天输入功能")
    print()
    
    print("选择测试模式:")
    print("1. 完整分析测试 (发送详细分析请求)")
    print("2. 快速测试 (发送简单消息)")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        print("\n开始完整分析测试...")
        success = test_chat_input()
    elif choice == "2":
        print("\n开始快速测试...")
        success = quick_test()
    else:
        print("无效选择，使用快速测试")
        success = quick_test()
    
    if success:
        print("\n🎉 测试成功完成！")
        print("生成的文件:")
        if choice == "1":
            print("- augment_chat_test_result_*.txt (完整分析结果)")
            print("- augment_chat_test_screenshot_*.png (截图)")
        else:
            print("- augment_quick_test_*.txt (快速测试结果)")
    else:
        print("\n❌ 测试未完成")
        print("请检查:")
        print("1. Augment插件是否正常工作")
        print("2. 光标是否在正确的输入框中")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
