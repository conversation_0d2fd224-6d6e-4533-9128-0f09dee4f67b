"""
简化版Augment直接输入工具
打开Augment后直接输入，不检查位置
"""

import time
import pyautogui
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_augment_analysis():
    """
    简化版Augment分析流程
    """
    print("=== 简化版Augment直接输入工具 ===")
    
    try:
        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        print("✅ 初始化完成")
        
        # 步骤1: 确认IDEA已打开
        print("\n🔍 步骤1: 确认环境")
        input("请确保IntelliJ IDEA已打开，然后按Enter继续...")
        
        # 步骤2: 打开Augment
        print("\n🚀 步骤2: 打开Augment")
        print("使用Action搜索打开Augment...")
        
        # 打开Action搜索
        pyautogui.hotkey('ctrl', 'shift', 'a')
        time.sleep(2)
        
        # 清空搜索框并输入Augment Chat
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.write("Augment Chat", interval=0.1)
        time.sleep(2)
        
        # 按Enter打开Augment
        pyautogui.press('enter')
        time.sleep(3)
        
        print("✅ Augment打开命令已执行")
        
        # 步骤3: 确认Augment已打开
        print("\n🔍 步骤3: 确认Augment状态")
        response = input("你能看到Augment聊天界面了吗？光标是否在输入框中？(y/n): ").strip().lower()
        
        if response != 'y':
            print("❌ Augment未正确打开")
            print("请手动打开Augment聊天界面，确保光标在输入框中")
            input("准备好后按Enter继续...")
        
        # 步骤4: 直接输入分析请求
        print("\n📝 步骤4: 发送分析请求")
        print("准备发送项目分析请求...")
        
        # 构造分析请求
        analysis_request = """请对这个AI辅助项目进行全面的代码分析，包括：

🏗️ **项目架构分析**
- 整体架构设计和模块组织
- 组件间的依赖关系和交互方式
- 设计模式的使用情况

📊 **代码质量评估**
- 代码规范性和一致性检查
- 可维护性和可读性分析
- 潜在的代码问题和技术债务

🔧 **技术栈分析**
- 使用的编程语言和框架
- 第三方库和依赖管理
- 技术选型的合理性评估

⚡ **性能和优化**
- 性能瓶颈识别和分析
- 资源使用效率评估
- 性能优化建议和改进方案

🔒 **安全性审查**
- 潜在的安全漏洞识别
- 数据处理和存储安全性
- 安全最佳实践建议

📈 **改进建议**
- 代码重构和优化建议
- 功能扩展和增强方向
- 开发流程和工具改进

请提供详细的分析报告，包含具体的代码示例、问题说明和实用的改进方案。"""
        
        # 复制到剪贴板
        pyperclip.copy(analysis_request)
        print("✅ 分析请求已复制到剪贴板")
        
        print("\n🎯 直接输入模式")
        print("假设光标已经在Augment输入框中，直接输入...")
        
        # 清空输入框（如果有内容）
        print("🧹 清空输入框...")
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        # 直接粘贴分析请求
        print("📋 粘贴分析请求...")
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(2)
        
        # 发送消息
        print("🚀 发送消息...")
        pyautogui.press('enter')
        time.sleep(2)
        
        print("✅ 分析请求已发送")
        
        # 步骤5: 确认消息发送成功
        print("\n🔍 步骤5: 确认发送状态")
        response = input("你能在Augment界面中看到刚才发送的分析请求吗？(y/n): ").strip().lower()
        
        if response == 'y':
            print("✅ 消息发送成功")
            
            # 步骤6: 等待分析完成
            print("\n⏳ 步骤6: 等待分析完成")
            wait_for_analysis()
            
            # 步骤7: 获取结果
            print("\n📋 步骤7: 获取分析结果")
            result = get_analysis_result()
            
            if result:
                save_analysis_result(result)
                print("\n🎉 分析完成！")
                return True
            else:
                print("⚠️ 未能获取到分析结果")
                return False
        else:
            print("❌ 消息发送失败")
            print("可能的原因:")
            print("1. 光标不在正确的输入框中")
            print("2. Augment插件未正确加载")
            print("3. 需要登录或配置Augment")
            
            # 提供手动操作指导
            print("\n🎯 手动操作模式")
            print("请手动操作:")
            print("1. 确认Augment聊天界面已打开")
            print("2. 点击输入框")
            print("3. 按 Ctrl+V 粘贴分析请求")
            print("4. 按 Enter 发送")
            
            input("手动发送完成后，按Enter继续...")
            
            # 继续等待和获取结果
            print("\n⏳ 等待分析完成...")
            wait_for_analysis()
            
            result = get_analysis_result()
            if result:
                save_analysis_result(result)
                print("\n🎉 分析完成！")
                return True
            else:
                print("⚠️ 未能获取到分析结果")
                return False
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

def wait_for_analysis():
    """
    等待Augment分析完成
    """
    print("⏳ 等待Augment分析完成...")
    print("请观察Augment界面，等待分析结果出现")
    
    # 简化的等待策略
    wait_intervals = [60, 60, 60, 60, 60]  # 每分钟检查一次，最多5分钟
    
    for i, interval in enumerate(wait_intervals):
        print(f"⏰ 等待阶段 {i+1}/{len(wait_intervals)} ({interval}秒)")
        
        # 倒计时
        for remaining in range(interval, 0, -10):
            print(f"   剩余 {remaining} 秒...", end='\r')
            time.sleep(10)
        
        print()  # 换行
        
        # 询问用户
        if i >= 0:  # 从第一次就开始询问
            try:
                response = input("分析完成了吗？(y=完成, n=继续等待, Enter=继续等待): ").strip().lower()
                if response == 'y':
                    print("✅ 用户确认分析完成")
                    return
                elif response == 'n':
                    print("⏳ 继续等待...")
                    continue
                else:
                    print("⏳ 继续等待...")
            except:
                print("⏳ 继续等待...")
    
    print("✅ 等待时间结束")

def get_analysis_result():
    """
    获取分析结果
    """
    try:
        print("📋 获取分析结果...")
        
        # 方法1: 全选复制
        print("🎯 尝试全选复制...")
        
        # 全选
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)
        
        # 复制
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)
        
        # 获取剪贴板内容
        result = pyperclip.paste()
        
        if result and len(result) > 100:
            print(f"✅ 成功获取结果 ({len(result)} 字符)")
            return result
        else:
            print("❌ 自动获取失败")
            
            # 方法2: 手动复制
            print("\n🎯 手动复制模式")
            print("请手动操作:")
            print("1. 在Augment界面中选择分析结果")
            print("2. 按 Ctrl+C 复制")
            print("3. 按Enter继续")
            
            input("复制完成后按Enter...")
            
            result = pyperclip.paste()
            
            if result and len(result) > 50:
                print(f"✅ 手动获取成功 ({len(result)} 字符)")
                return result
            else:
                print("❌ 仍未获取到有效结果")
                return None
                
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_analysis_result(result):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        filename = f"augment_simple_analysis_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment项目分析报告 (简化版) ===\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目: AI-assisted\n")
            f.write(f"工具版本: simple_augment_direct v1.0\n")
            f.write("=" * 60 + "\n\n")
            f.write(result)
        
        print(f"📁 分析结果已保存到: {filename}")
        
        # 显示预览
        print("\n📄 分析结果预览:")
        print("-" * 50)
        preview = result[:500] + "..." if len(result) > 500 else result
        print(preview)
        print("-" * 50)
        
        # 保存截图
        try:
            screenshot_file = f"augment_simple_screenshot_{timestamp}.png"
            screenshot = pyautogui.screenshot()
            screenshot.save(screenshot_file)
            print(f"📸 截图已保存: {screenshot_file}")
        except Exception as e:
            print(f"⚠️ 截图保存失败: {e}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def main():
    """
    主函数
    """
    print("=== 简化版Augment直接输入工具 ===")
    print("专门解决直接输入问题，不检查坐标位置")
    print()
    
    print("工作流程:")
    print("1. 打开Augment (使用Action搜索)")
    print("2. 直接在当前位置输入分析请求")
    print("3. 等待分析完成")
    print("4. 获取并保存结果")
    print()
    
    if input("开始分析吗？(y/n): ").strip().lower() != 'y':
        print("操作取消")
        return
    
    success = simple_augment_analysis()
    
    if success:
        print("\n🎉 项目分析成功完成！")
        print("生成的文件:")
        print("- augment_simple_analysis_*.txt (分析结果)")
        print("- augment_simple_screenshot_*.png (截图)")
    else:
        print("\n❌ 分析未完成")
        print("请检查Augment插件状态和网络连接")

if __name__ == "__main__":
    main()
