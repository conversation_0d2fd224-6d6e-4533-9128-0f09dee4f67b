"""
直接输入版Augment测试
不使用坐标点击，直接在当前光标位置输入
"""

import time
import pyautogui
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def direct_input_test():
    """
    直接输入测试 - 不使用坐标点击
    """
    print("=== 直接输入版Augment测试 ===")
    
    try:
        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5  # 减少延迟
        
        print("✅ 初始化完成")
        
        # 确认状态
        print("🔍 当前状态:")
        print("- Augment插件已打开")
        print("- 光标已在输入框中")
        print("- 准备直接输入文字")
        
        input("请确认Augment聊天界面已打开且光标在输入框中，然后按Enter继续...")
        
        # 构造分析提示
        analysis_prompt = """请分析这个AI辅助项目的代码结构，包括：

🏗️ **项目架构分析**
- 整体架构设计
- 模块组织和依赖关系
- 设计模式使用情况

📊 **代码质量评估**
- 代码规范性检查
- 可维护性分析
- 潜在问题识别

🔧 **技术栈分析**
- 使用的编程语言和框架
- 第三方库依赖分析
- 技术选型合理性

⚡ **性能和优化**
- 性能瓶颈识别
- 资源使用分析
- 优化建议

🔒 **安全性审查**
- 安全漏洞检查
- 数据处理安全性
- 安全最佳实践

📈 **改进建议**
- 代码重构建议
- 功能扩展方向
- 开发最佳实践

请提供详细的分析报告，包含具体的代码示例和实用的改进方案。"""
        
        print("📝 准备发送分析请求...")
        print("分析提示内容:")
        print("-" * 50)
        print(analysis_prompt[:200] + "...")
        print("-" * 50)
        
        # 复制到剪贴板
        pyperclip.copy(analysis_prompt)
        print("✅ 分析提示已复制到剪贴板")
        
        # 方法1: 直接粘贴
        print("\n🎯 方法1: 直接粘贴输入")
        
        try:
            # 清空可能的现有内容
            print("🧹 清空输入框...")
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)
            pyautogui.press('delete')
            time.sleep(0.5)
            
            # 直接粘贴
            print("📋 粘贴分析请求...")
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(2)
            
            # 发送消息
            print("🚀 发送消息...")
            pyautogui.press('enter')
            time.sleep(2)
            
            print("✅ 消息已发送 (方法1)")
            
        except Exception as e:
            print(f"❌ 方法1失败: {e}")
            
            # 方法2: 逐行输入
            print("\n🎯 方法2: 逐行输入")
            
            try:
                # 清空输入框
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.5)
                pyautogui.press('delete')
                time.sleep(0.5)
                
                # 分行输入
                lines = analysis_prompt.split('\n')
                
                for i, line in enumerate(lines):
                    if line.strip():  # 跳过空行
                        print(f"📝 输入第 {i+1} 行...")
                        pyautogui.write(line.strip(), interval=0.02)
                        pyautogui.press('enter')
                        time.sleep(0.1)
                
                # 发送消息
                print("🚀 发送消息...")
                pyautogui.press('enter')
                time.sleep(2)
                
                print("✅ 消息已发送 (方法2)")
                
            except Exception as e2:
                print(f"❌ 方法2也失败: {e2}")
                
                # 方法3: 手动确认
                print("\n🎯 方法3: 手动操作")
                print("自动输入失败，请手动操作:")
                print("1. 确认光标在Augment输入框中")
                print("2. 按 Ctrl+V 粘贴分析请求")
                print("3. 按 Enter 发送消息")
                
                input("手动发送完成后，按Enter继续...")
        
        # 等待响应
        print("\n⏳ 等待Augment分析...")
        wait_for_response()
        
        # 获取结果
        print("\n📋 获取分析结果...")
        result = get_result()
        
        if result:
            save_result(result)
            print("🎉 测试完成！")
            return True
        else:
            print("⚠️ 未能获取到结果")
            return False
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def wait_for_response():
    """
    等待Augment响应
    """
    print("⏳ 智能等待Augment响应...")
    print("观察Augment界面，等待分析结果出现")
    
    # 简化的等待策略
    wait_times = [30, 30, 30, 30, 30, 30]  # 每30秒检查一次，总共3分钟
    
    for i, wait_time in enumerate(wait_times):
        print(f"⏰ 等待阶段 {i+1}/{len(wait_times)} ({wait_time}秒)")
        
        # 倒计时显示
        for remaining in range(wait_time, 0, -5):
            print(f"   剩余 {remaining} 秒...", end='\r')
            time.sleep(5)
        
        print()  # 换行
        
        # 询问用户
        if i >= 1:  # 1分钟后开始询问
            try:
                response = input("分析完成了吗？(y=完成, n=继续等待, Enter=继续等待): ").strip().lower()
                if response == 'y':
                    print("✅ 用户确认分析完成")
                    break
                elif response == 'n':
                    print("⏳ 继续等待...")
                    continue
                else:
                    print("⏳ 继续等待...")
            except:
                print("⏳ 继续等待...")
    
    print("✅ 等待阶段完成")

def get_result():
    """
    获取分析结果
    """
    try:
        print("📋 尝试获取分析结果...")
        
        # 方法1: 全选复制
        print("🎯 方法1: 全选复制")
        
        # 全选所有内容
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)
        
        # 复制到剪贴板
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)
        
        # 获取剪贴板内容
        result = pyperclip.paste()
        
        if result and len(result) > 100:
            print(f"✅ 成功获取结果 ({len(result)} 字符)")
            return result
        else:
            print("❌ 自动获取结果失败或内容太短")
            
            # 方法2: 手动复制
            print("\n🎯 方法2: 手动复制")
            print("请手动操作:")
            print("1. 选择Augment的分析结果文本")
            print("2. 按 Ctrl+C 复制")
            print("3. 然后按Enter继续")
            
            input("复制完成后按Enter...")
            
            result = pyperclip.paste()
            
            if result and len(result) > 50:
                print(f"✅ 手动获取成功 ({len(result)} 字符)")
                return result
            else:
                print("❌ 仍未获取到有效结果")
                return None
                
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_result(result):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        filename = f"augment_direct_analysis_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment项目分析报告 (直接输入版) ===\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目: AI-assisted\n")
            f.write(f"方法: 直接输入 (无坐标点击)\n")
            f.write("=" * 60 + "\n\n")
            f.write(result)
        
        print(f"📁 分析结果已保存到: {filename}")
        
        # 显示结果预览
        print("\n📄 分析结果预览:")
        print("-" * 50)
        preview = result[:500] + "..." if len(result) > 500 else result
        print(preview)
        print("-" * 50)
        
        # 保存截图
        try:
            screenshot_file = f"augment_direct_screenshot_{timestamp}.png"
            screenshot = pyautogui.screenshot()
            screenshot.save(screenshot_file)
            print(f"📸 截图已保存: {screenshot_file}")
        except Exception as e:
            print(f"⚠️ 截图保存失败: {e}")
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def quick_test():
    """
    快速测试 - 只发送简单消息
    """
    print("=== 快速测试模式 ===")
    
    try:
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
        print("准备发送简单测试消息...")
        input("确认光标在Augment输入框中，按Enter继续...")
        
        # 简单的测试消息
        test_message = "Hello Augment! 请简单介绍一下这个项目的主要功能。"
        
        # 复制到剪贴板
        pyperclip.copy(test_message)
        print("✅ 测试消息已复制到剪贴板")
        
        # 清空并粘贴
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(1)
        
        # 发送
        pyautogui.press('enter')
        time.sleep(2)
        
        print("✅ 测试消息已发送")
        print("请观察Augment是否有响应")
        
        input("看到响应后按Enter...")
        
        # 简单获取结果
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(1)
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(2)
        
        result = pyperclip.paste()
        
        if result and len(result) > 20:
            print(f"✅ 获取到响应 ({len(result)} 字符)")
            print("响应预览:")
            print("-" * 30)
            print(result[:200] + "..." if len(result) > 200 else result)
            print("-" * 30)
            return True
        else:
            print("❌ 未获取到有效响应")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("=== 直接输入版Augment测试工具 ===")
    print("专门解决输入问题 - 不使用坐标点击")
    print()
    
    print("选择测试模式:")
    print("1. 完整分析测试 (发送详细分析请求)")
    print("2. 快速测试 (发送简单消息)")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == "1":
        print("\n开始完整分析测试...")
        success = direct_input_test()
    elif choice == "2":
        print("\n开始快速测试...")
        success = quick_test()
    else:
        print("无效选择，使用快速测试模式")
        success = quick_test()
    
    if success:
        print("\n🎉 测试成功完成！")
        if choice == "1":
            print("生成的文件:")
            print("- augment_direct_analysis_*.txt (分析结果)")
            print("- augment_direct_screenshot_*.png (截图)")
    else:
        print("\n❌ 测试未完成")
        print("请检查:")
        print("1. Augment插件是否正常工作")
        print("2. 光标是否在正确的输入框中")
        print("3. 是否有网络连接问题")

if __name__ == "__main__":
    main()
