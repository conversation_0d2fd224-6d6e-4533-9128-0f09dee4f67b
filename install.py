#!/usr/bin/env python3
"""
Augment监听系统安装脚本
"""

import subprocess
import sys
import os

def install_dependencies():
    """
    安装依赖
    """
    print("=== Augment监听系统安装 ===")
    print("正在安装依赖...")
    
    try:
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        
        # 检查关键依赖
        try:
            import pyautogui
            import pyperclip
            print("✅ 核心依赖验证成功")
        except ImportError as e:
            print(f"❌ 核心依赖验证失败: {e}")
            return False
        
        print("\n🎉 安装完成！")
        print("\n使用方法:")
        print("python start_augment_monitor.py  # 启动自动业务分析")
        print("或")
        print("python augment_monitor_system.py  # 直接运行分析系统")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def main():
    """
    主函数
    """
    print("=== AI辅助项目自动业务分析系统安装 ===")
    success = install_dependencies()

    if success:
        print("\n✅ 安装成功！")
        print("🚀 系统已准备就绪，可以开始自动业务分析")
        print("\n启动命令:")
        print("python start_augment_monitor.py")

        # 自动启动业务分析系统
        try:
            print("\n正在自动启动业务分析系统...")
            from augment_monitor_system import main as monitor_main
            monitor_main()
        except KeyboardInterrupt:
            print("\n👋 用户中断启动")
        except Exception as e:
            print(f"自动启动失败: {e}")
            print("请手动运行: python start_augment_monitor.py")
    else:
        print("\n❌ 安装失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
