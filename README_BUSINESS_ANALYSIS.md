# AI辅助项目自动业务分析系统

## 项目概述

这是一个基于Augment AI的自动化业务分析系统，专门用于分析AI辅助项目的业务流程、技术架构和改进建议。系统已经过优化，无需手动输入，自动执行全面的项目业务分析。

## 主要特性

### 🚀 自动化分析
- **无需手动输入**：系统使用预设的专业业务分析提示词
- **一键启动**：运行脚本即可开始完整的业务分析
- **智能监听**：自动捕获Augment AI的分析响应

### 📊 全面分析维度
1. **项目概述**：目的、功能、价值、用户群体
2. **技术架构**：代码结构、技术栈、设计模式、数据流
3. **业务流程**：核心流程、用户交互、数据处理、异常处理
4. **功能特性**：已实现功能、优势特点、性能分析
5. **代码质量**：组织结构、错误处理、安全性、测试覆盖
6. **改进建议**：架构优化、功能增强、性能提升、用户体验
7. **技术债务**：风险识别、重构建议、依赖管理、兼容性

### 💾 增强数据存储
- **结构化JSON输出**：专门针对业务分析的数据格式
- **数据库存储**：SQLite数据库保存分析历史
- **文件命名优化**：`business_analysis_YYYYMMDD_HHMMSS.json`
- **元数据丰富**：包含分析类型、项目信息、处理时间等

## 快速开始

### 1. 安装依赖
```bash
python install.py
```

### 2. 启动业务分析
```bash
python start_augment_monitor.py
```

或直接运行：
```bash
python augment_monitor_system.py
```

## 系统要求

### 软件环境
- Python 3.8+
- Augment AI插件（IntelliJ IDEA或VS Code）
- 相关Python依赖包（见requirements.txt）

### 硬件要求
- 支持GUI自动化的操作系统
- 足够的屏幕分辨率（推荐1920x1080以上）

## 输出文件说明

### JSON分析报告
系统会生成格式化的JSON文件，包含：

```json
{
  "analysis_type": "business_process_analysis",
  "project_info": {
    "name": "AI-assisted Project",
    "analysis_date": "2025-08-01T...",
    "workspace_path": "D:/project/AI-assisted"
  },
  "request": {
    "message": "详细的业务分析提示词...",
    "analysis_scope": "complete_business_analysis"
  },
  "response": {
    "content": "AI分析结果...",
    "analysis_categories": [
      "project_overview",
      "technical_architecture",
      "business_process",
      "feature_assessment",
      "code_quality",
      "improvement_suggestions",
      "technical_debt_risks"
    ]
  },
  "metadata": {
    "system": "AugmentMonitorSystem",
    "version": "2.0_business_analysis",
    "analysis_mode": "automated_business_analysis"
  }
}
```

### 数据库记录
- 表名：`augment_responses`
- 包含完整的请求响应数据
- 支持历史查询和统计分析

## 配置说明

### 输入位置配置
默认输入位置：`(1800, 900)`
可在代码中修改：
```python
monitor = AugmentMonitorSystem(input_position=(x, y))
```

### 业务分析提示词
系统使用固定的专业提示词，涵盖7个主要分析维度。如需自定义，可修改 `get_business_analysis_prompt()` 方法。

## 日志和监控

### 日志文件
- 文件：`augment_monitor.log`
- 级别：INFO, ERROR, DEBUG
- 包含详细的执行过程和错误信息

### 实时监控
系统运行时会显示：
- 分析进度
- 响应质量评分
- 数据保存状态
- 错误和警告信息

## 故障排除

### 常见问题
1. **连接失败**：确保Augment AI插件正常运行
2. **位置错误**：调整输入位置坐标
3. **权限问题**：确保Python有GUI自动化权限
4. **依赖缺失**：运行 `pip install -r requirements.txt`

### 调试模式
启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 版本历史

### v2.0 - 自动业务分析版本
- 移除所有手动输入需求
- 增加固定的专业业务分析提示词
- 优化JSON输出格式
- 增强错误处理和日志记录

### v1.0 - 基础监听版本
- 基本的Augment监听功能
- 交互式用户输入
- 简单的数据存储

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. Augment AI插件状态
3. 系统权限设置
4. 网络连接状况

## 许可证

本项目遵循MIT许可证。
