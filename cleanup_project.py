"""
项目清理脚本
整理AI-assisted项目，删除无效和重复的文件
"""

import os
import shutil
import json
from pathlib import Path

def analyze_project_files():
    """
    分析项目文件并分类
    """
    
    # 需要保留的核心文件
    keep_files = {
        "enhanced_augment_test.py",  # 主要工具
        "requirements.txt",          # 依赖管理
        "README.md",                 # 项目说明
        "README_BUSINESS_ANALYSIS.md", # 业务分析说明
        "智能项目分析器使用说明.md",    # 使用说明
    }
    
    # 需要保留的目录
    keep_directories = {
        "services",     # 核心服务
        "logs",         # 日志目录
        "reports",      # 报告目录
        "static",       # 静态文件
        "tasks",        # 任务目录
        "temp",         # 临时目录
        "tools",        # 工具目录
    }
    
    # 可能删除的测试和重复文件
    delete_candidates = {
        # 重复的测试文件
        "augment_chat_opener.py",      # 被enhanced_augment_test.py替代
        "direct_augment_input.py",     # 被enhanced_augment_test.py替代
        "fixed_augment_test.py",       # 被enhanced_augment_test.py替代
        "simple_augment_direct.py",    # 被enhanced_augment_test.py替代
        "test_window_detection.py",    # 测试文件
        "test_chat_only.py",          # 测试文件
        "test_augment_json_save.py",  # 测试文件
        
        # 旧版本的分析器
        "analyze_yard_project.py",     # 特定项目分析器
        "demo_business_analysis.py",   # 演示文件
        "demo_json_analysis.py",       # 演示文件
        
        # 监控相关（如果不需要）
        "augment_monitor_system.py",   # 监控系统
        "start_augment_monitor.py",    # 监控启动
        "augment_monitor.log",         # 监控日志
        
        # 批处理文件
        "analyze_project.bat",         # 批处理文件
        "install.py",                  # 安装脚本
        
        # 临时响应文件
        "augment_response_1754029092.json",
        "augment_response_1754029912.json",
        "augment_responses.db",
        "business_analysis_report.json",
        "system_response_20250801_145614.json",
        "system_response_20250801_150450.json",
        
        # 特定项目的提示文件
        "AI-assisted_analysis_prompt.txt",
        "yard-entrance-management_analysis_prompt.txt",
    }
    
    # 需要用户确认的文件/目录
    confirm_delete = {
        "augment_protos": "protobuf文件目录 - 如果不需要gRPC功能可以删除",
        "smart_project_analyzer.py": "智能项目分析器 - 如果enhanced_augment_test.py已包含此功能可以删除",
        "auto_analyze_project.py": "自动分析项目 - 如果enhanced_augment_test.py已包含此功能可以删除",
    }
    
    return {
        "keep_files": keep_files,
        "keep_directories": keep_directories,
        "delete_candidates": delete_candidates,
        "confirm_delete": confirm_delete
    }

def generate_cleanup_report():
    """
    生成清理报告
    """
    analysis = analyze_project_files()
    
    report = """# 🧹 AI-assisted项目清理报告

## ✅ 保留的核心文件
"""
    
    for file in sorted(analysis["keep_files"]):
        if os.path.exists(file):
            size = os.path.getsize(file)
            report += f"- `{file}` ({size:,} bytes)\n"
    
    report += "\n## ✅ 保留的核心目录\n"
    for directory in sorted(analysis["keep_directories"]):
        if os.path.exists(directory):
            file_count = sum(len(files) for _, _, files in os.walk(directory))
            report += f"- `{directory}/` ({file_count} files)\n"
    
    report += "\n## ❌ 建议删除的文件\n"
    total_delete_size = 0
    for file in sorted(analysis["delete_candidates"]):
        if os.path.exists(file):
            if os.path.isfile(file):
                size = os.path.getsize(file)
                total_delete_size += size
                report += f"- `{file}` ({size:,} bytes)\n"
            elif os.path.isdir(file):
                file_count = sum(len(files) for _, _, files in os.walk(file))
                report += f"- `{file}/` ({file_count} files)\n"
    
    report += f"\n**删除这些文件可以节省约 {total_delete_size:,} bytes**\n"
    
    report += "\n## ⚠️ 需要确认的文件/目录\n"
    for item, description in analysis["confirm_delete"].items():
        if os.path.exists(item):
            if os.path.isfile(item):
                size = os.path.getsize(item)
                report += f"- `{item}` ({size:,} bytes) - {description}\n"
            elif os.path.isdir(item):
                file_count = sum(len(files) for _, _, files in os.walk(item))
                report += f"- `{item}/` ({file_count} files) - {description}\n"
    
    report += """
## 🎯 清理建议

### 立即可以删除
运行以下命令删除明确无用的文件：
```bash
python cleanup_project.py --delete-safe
```

### 需要确认后删除
检查以下项目是否还需要：
1. `augment_protos/` - 如果只使用UI自动化，不需要gRPC，可以删除
2. `smart_project_analyzer.py` - 如果功能已集成到enhanced_augment_test.py中，可以删除
3. `auto_analyze_project.py` - 同上

### 清理后的项目结构
```
AI-assisted/
├── enhanced_augment_test.py    # 主要工具
├── requirements.txt            # 依赖管理
├── README.md                   # 项目说明
├── services/                   # 核心服务
├── tools/                      # 工具目录
├── logs/                       # 日志
├── reports/                    # 报告
└── temp/                       # 临时文件
```
"""
    
    return report

def delete_safe_files():
    """
    删除明确可以删除的文件
    """
    analysis = analyze_project_files()
    deleted_files = []
    deleted_size = 0
    
    for file in analysis["delete_candidates"]:
        if os.path.exists(file):
            try:
                if os.path.isfile(file):
                    size = os.path.getsize(file)
                    os.remove(file)
                    deleted_files.append(f"{file} ({size:,} bytes)")
                    deleted_size += size
                elif os.path.isdir(file):
                    file_count = sum(len(files) for _, _, files in os.walk(file))
                    shutil.rmtree(file)
                    deleted_files.append(f"{file}/ ({file_count} files)")
                    
                print(f"✅ 已删除: {file}")
                
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
    
    # 删除__pycache__目录
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                cache_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(cache_path)
                    deleted_files.append(f"{cache_path}/ (cache)")
                    print(f"✅ 已删除缓存: {cache_path}")
                except Exception as e:
                    print(f"❌ 删除缓存失败 {cache_path}: {e}")
    
    print(f"\n🎉 清理完成！")
    print(f"删除了 {len(deleted_files)} 个文件/目录")
    print(f"节省了约 {deleted_size:,} bytes")
    
    return deleted_files

def main():
    """
    主函数
    """
    import sys
    
    print("🧹 AI-assisted项目清理工具")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--delete-safe":
        print("开始删除安全的文件...")
        deleted_files = delete_safe_files()
        
        # 保存删除记录
        with open("cleanup_log.json", "w", encoding="utf-8") as f:
            json.dump({
                "deleted_files": deleted_files,
                "cleanup_time": str(os.path.getctime(".")),
                "tool_version": "cleanup_project.py v1.0"
            }, f, indent=2, ensure_ascii=False)
        
        print("删除记录已保存到 cleanup_log.json")
        
    else:
        # 生成清理报告
        report = generate_cleanup_report()
        
        # 保存报告
        with open("cleanup_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print("📋 清理报告已生成: cleanup_report.md")
        print("\n预览:")
        print(report[:1000] + "..." if len(report) > 1000 else report)
        
        print("\n下一步:")
        print("1. 查看 cleanup_report.md 了解详细信息")
        print("2. 运行 'python cleanup_project.py --delete-safe' 删除安全的文件")
        print("3. 手动确认并删除需要确认的文件")

if __name__ == "__main__":
    main()
