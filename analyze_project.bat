@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 完全自动化项目分析器
echo ========================================
echo 功能：自动打开IDEA + 自动发送Augment + 自动生成报告
echo ========================================
echo.

if "%1"=="" (
    echo 使用方法:
    echo   analyze_project.bat "项目路径"
    echo.
    echo 示例:
    echo   analyze_project.bat "D:\my-java-project"
    echo   analyze_project.bat "C:\Users\<USER>\Documents\my-python-app"
    echo   analyze_project.bat "E:\workspace\my-react-app"
    echo.
    set /p project_path="请输入项目路径: "
) else (
    set project_path=%1
)

echo 📁 开始自动分析项目: %project_path%
echo.

python auto_analyze_project.py "%project_path%"

echo.
echo 按任意键退出...
pause >nul
