"""
Augment聊天面板打开器
专门用于打开Augment的聊天界面
"""

import time
import pyautogui
import pyperclip
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def open_augment_chat():
    """
    专门打开Augment聊天面板
    """
    print("=== Augment聊天面板打开器 ===")
    
    try:
        # 设置pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 1
        
        print("🔍 当前状态检查...")
        input("请确保IntelliJ IDEA已打开，然后按Enter继续...")
        
        # 方法1: 使用Action搜索打开Augment Chat
        print("\n🎯 方法1: 使用Action搜索打开Augment Chat")
        
        # 打开Action搜索
        pyautogui.hotkey('ctrl', 'shift', 'a')
        time.sleep(2)
        
        # 清空搜索框
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        
        # 输入"Augment Chat"或"Augment"
        pyautogui.write("Augment Chat", interval=0.1)
        time.sleep(2)
        
        # 按Enter选择第一个结果
        pyautogui.press('enter')
        time.sleep(3)
        
        print("✅ Action搜索命令已执行")
        
        # 检查是否成功打开
        if check_chat_panel_opened():
            print("✅ 聊天面板已打开")
            return True
        
        # 方法2: 尝试其他搜索词
        print("\n🎯 方法2: 尝试其他搜索词")
        
        search_terms = ["Augment", "Chat", "AI Assistant", "Copilot"]
        
        for term in search_terms:
            print(f"🔍 尝试搜索: {term}")
            
            # 打开Action搜索
            pyautogui.hotkey('ctrl', 'shift', 'a')
            time.sleep(2)
            
            # 清空并输入搜索词
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.5)
            pyautogui.write(term, interval=0.1)
            time.sleep(2)
            
            # 按Enter
            pyautogui.press('enter')
            time.sleep(3)
            
            if check_chat_panel_opened():
                print(f"✅ 使用'{term}'成功打开聊天面板")
                return True
        
        # 方法3: 尝试工具窗口菜单
        print("\n🎯 方法3: 通过工具窗口菜单")
        
        # 使用Alt+V打开View菜单
        pyautogui.hotkey('alt', 'v')
        time.sleep(1)
        
        # 查找Tool Windows
        pyautogui.press('t')  # 快速导航到Tool Windows
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(1)
        
        # 查找Augment
        pyautogui.press('a')  # 快速导航到A开头的项目
        time.sleep(1)
        pyautogui.press('enter')
        time.sleep(3)
        
        if check_chat_panel_opened():
            print("✅ 通过工具窗口菜单成功打开")
            return True
        
        # 方法4: 手动指导
        print("\n🎯 方法4: 手动指导")
        print("自动打开失败，请手动操作:")
        print("1. 在IntelliJ IDEA中按 Ctrl+Shift+A")
        print("2. 输入 'Augment' 或 'Augment Chat'")
        print("3. 选择Augment相关的选项")
        print("4. 或者查看IDE的侧边栏是否有Augment标签")
        print("5. 或者在菜单 View -> Tool Windows 中查找Augment")
        
        input("请手动打开Augment聊天面板，然后按Enter继续...")
        
        if check_chat_panel_opened():
            print("✅ 手动打开成功")
            return True
        else:
            print("❌ 仍未检测到聊天面板")
            return False
        
    except Exception as e:
        print(f"❌ 打开聊天面板失败: {e}")
        return False

def check_chat_panel_opened():
    """
    检查聊天面板是否已打开
    """
    try:
        print("🔍 检查聊天面板状态...")
        
        # 尝试在多个可能的聊天输入位置输入测试文本
        chat_input_positions = [
            # 常见的聊天输入框位置
            (960, 800),   # 中下方
            (960, 850),   # 更下方
            (960, 900),   # 底部
            (800, 800),   # 左侧
            (1120, 800),  # 右侧
            (960, 750),   # 稍上方
            (700, 850),   # 左下
            (1200, 850),  # 右下
        ]
        
        for i, (x, y) in enumerate(chat_input_positions):
            try:
                print(f"🎯 检查位置 {i+1}: ({x}, {y})")
                
                # 点击位置
                pyautogui.click(x, y)
                time.sleep(1)
                
                # 尝试输入测试文本
                test_text = "test_chat_input"
                pyautogui.write(test_text, interval=0.05)
                time.sleep(1)
                
                # 检查是否真的输入了文本
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.5)
                pyautogui.hotkey('ctrl', 'c')
                time.sleep(0.5)
                
                clipboard_content = pyperclip.paste()
                
                # 清除测试文本
                pyautogui.press('delete')
                time.sleep(0.5)
                
                # 如果剪贴板包含我们的测试文本，说明找到了输入框
                if test_text in clipboard_content:
                    print(f"✅ 在位置 ({x}, {y}) 找到聊天输入框")
                    return True
                else:
                    print(f"❌ 位置 ({x}, {y}) 不是聊天输入框")
                
            except Exception as e:
                print(f"❌ 检查位置 {i+1} 失败: {e}")
                continue
        
        print("❌ 未找到有效的聊天输入框")
        return False
        
    except Exception as e:
        print(f"❌ 检查聊天面板失败: {e}")
        return False

def test_chat_input():
    """
    测试聊天输入功能 - 改进版，不使用坐标点击
    """
    print("\n📝 测试聊天输入功能...")

    # 构造详细的分析请求
    analysis_message = """请分析这个AI辅助项目的代码结构，包括：

🏗️ **项目架构分析**
- 整体架构设计
- 模块组织和依赖关系
- 设计模式使用情况

📊 **代码质量评估**
- 代码规范性检查
- 可维护性分析
- 潜在问题识别

🔧 **技术栈分析**
- 使用的编程语言和框架
- 第三方库依赖分析
- 技术选型合理性

⚡ **性能和优化**
- 性能瓶颈识别
- 资源使用分析
- 优化建议

🔒 **安全性审查**
- 安全漏洞检查
- 数据处理安全性
- 安全最佳实践

📈 **改进建议**
- 代码重构建议
- 功能扩展方向
- 开发最佳实践

请提供详细的分析报告，包含具体的代码示例和实用的改进方案。"""

    # 复制到剪贴板
    pyperclip.copy(analysis_message)
    print("✅ 分析请求已复制到剪贴板")

    print("\n🎯 直接输入模式 (不使用坐标点击)")
    print("当前光标应该已经在Augment输入框中")

    try:
        # 方法1: 直接粘贴
        print("📋 方法1: 直接粘贴输入")

        # 清空现有内容
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.5)
        pyautogui.press('delete')
        time.sleep(0.5)

        # 粘贴分析请求
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(2)

        # 发送消息
        pyautogui.press('enter')
        time.sleep(2)

        print("✅ 分析请求已发送")

        # 询问用户是否看到消息
        response = input("你能在Augment界面中看到这条分析请求吗？(y/n): ").strip().lower()

        if response == 'y':
            print("✅ 聊天功能正常工作")

            # 等待分析完成
            print("\n⏳ 等待Augment分析完成...")
            wait_for_analysis_complete()

            # 获取分析结果
            result = get_analysis_result()

            if result:
                save_analysis_result(result)
                return True
            else:
                print("⚠️ 未能获取分析结果")
                return True  # 发送成功，但获取结果失败
        else:
            print("❌ 消息未正确发送")

            # 方法2: 手动操作
            print("\n🎯 方法2: 手动操作")
            print("请手动操作:")
            print("1. 确认光标在Augment输入框中")
            print("2. 按 Ctrl+V 粘贴分析请求")
            print("3. 按 Enter 发送消息")

            input("手动发送完成后，按Enter继续...")

            # 等待分析
            print("\n⏳ 等待Augment分析完成...")
            wait_for_analysis_complete()

            # 获取结果
            result = get_analysis_result()

            if result:
                save_analysis_result(result)

            return True

    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        return False

def wait_for_analysis_complete():
    """
    等待分析完成
    """
    print("观察Augment界面，等待分析结果出现...")

    # 智能等待策略
    wait_intervals = [30, 30, 30, 30, 30, 30]  # 每30秒检查一次，总共3分钟

    for i, interval in enumerate(wait_intervals):
        print(f"⏰ 等待阶段 {i+1}/{len(wait_intervals)} ({interval}秒)")

        # 倒计时
        for remaining in range(interval, 0, -10):
            print(f"   剩余 {remaining} 秒...", end='\r')
            time.sleep(10)

        print()  # 换行

        # 1分钟后开始询问
        if i >= 1:
            try:
                response = input("分析完成了吗？(y=完成, n=继续等待, Enter=继续等待): ").strip().lower()
                if response == 'y':
                    print("✅ 用户确认分析完成")
                    break
                elif response == 'n':
                    print("⏳ 继续等待...")
                    continue
                else:
                    print("⏳ 继续等待...")
            except:
                print("⏳ 继续等待...")

def get_analysis_result():
    """
    获取分析结果
    """
    try:
        print("\n📋 获取分析结果...")

        # 全选并复制
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(2)

        pyautogui.hotkey('ctrl', 'c')
        time.sleep(3)

        result = pyperclip.paste()

        if result and len(result) > 100:
            print(f"✅ 成功获取分析结果 ({len(result)} 字符)")
            return result
        else:
            print("❌ 自动获取失败")
            print("请手动复制Augment的分析结果")
            input("复制完成后按Enter...")

            result = pyperclip.paste()
            return result if result and len(result) > 50 else None

    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return None

def save_analysis_result(result):
    """
    保存分析结果
    """
    try:
        timestamp = int(time.time())
        filename = f"augment_analysis_result_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== Augment项目分析报告 ===\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目: AI-assisted\n")
            f.write("=" * 60 + "\n\n")
            f.write(result)

        print(f"📁 分析结果已保存到: {filename}")

        # 显示预览
        print("\n📄 分析结果预览:")
        print("-" * 50)
        preview = result[:500] + "..." if len(result) > 500 else result
        print(preview)
        print("-" * 50)

        # 保存截图
        try:
            screenshot_file = f"augment_analysis_screenshot_{timestamp}.png"
            pyautogui.screenshot().save(screenshot_file)
            print(f"📸 截图已保存: {screenshot_file}")
        except Exception as e:
            print(f"⚠️ 截图保存失败: {e}")

    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def main():
    """
    主函数
    """
    print("=== Augment聊天面板打开器 ===")
    print("专门用于打开和测试Augment聊天功能")
    print()
    
    print("操作步骤:")
    print("1. 尝试自动打开Augment聊天面板")
    print("2. 验证聊天面板是否正常工作")
    print("3. 测试消息发送功能")
    print()
    
    if input("开始操作吗？(y/n): ").strip().lower() != 'y':
        print("操作取消")
        return
    
    # 步骤1: 打开聊天面板
    print("\n=== 步骤1: 打开Augment聊天面板 ===")
    
    if open_augment_chat():
        print("✅ 聊天面板打开成功")
        
        # 步骤2: 测试聊天功能
        print("\n=== 步骤2: 测试聊天功能 ===")
        
        if test_chat_input():
            print("✅ 聊天功能测试成功")
            print("\n🎉 Augment聊天面板已就绪！")
            print("现在你可以正常使用Augment进行代码分析了。")
        else:
            print("❌ 聊天功能测试失败")
            print("请检查Augment插件是否正确安装和配置")
    else:
        print("❌ 无法打开聊天面板")
        print("请检查:")
        print("1. Augment插件是否已安装")
        print("2. 插件是否已启用")
        print("3. 是否需要登录或配置")

if __name__ == "__main__":
    main()
