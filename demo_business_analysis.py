#!/usr/bin/env python3
"""
AI辅助项目自动业务分析系统演示脚本
"""

import sys
import os
from datetime import datetime

def show_system_info():
    """
    显示系统信息
    """
    print("=" * 70)
    print("🚀 AI辅助项目自动业务分析系统")
    print("=" * 70)
    print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print()

def show_analysis_scope():
    """
    显示分析范围
    """
    print("📊 业务分析范围:")
    print("   ✓ 1. 项目概述 - 目的、功能、价值、用户群体")
    print("   ✓ 2. 技术架构 - 代码结构、技术栈、设计模式、数据流")
    print("   ✓ 3. 业务流程 - 核心流程、用户交互、数据处理、异常处理")
    print("   ✓ 4. 功能特性 - 已实现功能、优势特点、性能分析")
    print("   ✓ 5. 代码质量 - 组织结构、错误处理、安全性、测试覆盖")
    print("   ✓ 6. 改进建议 - 架构优化、功能增强、性能提升、用户体验")
    print("   ✓ 7. 技术债务 - 风险识别、重构建议、依赖管理、兼容性")
    print()

def show_system_features():
    """
    显示系统特性
    """
    print("🎯 系统特性:")
    print("   🔄 自动化执行 - 无需手动输入，一键启动分析")
    print("   🧠 智能分析 - 基于Augment AI的专业业务分析")
    print("   💾 数据存储 - SQLite数据库 + JSON文件双重保存")
    print("   📈 质量评估 - 智能响应质量评分系统")
    print("   📝 详细日志 - 完整的执行过程记录")
    print("   🔍 历史查询 - 支持分析历史数据统计")
    print()

def demo_prompt_preview():
    """
    演示提示词预览
    """
    try:
        from augment_monitor_system import AugmentMonitorSystem
        
        print("📝 业务分析提示词预览:")
        print("-" * 50)
        
        monitor = AugmentMonitorSystem()
        prompt = monitor.business_analysis_prompt
        
        # 显示提示词的前300字符
        preview = prompt[:300] + "..." if len(prompt) > 300 else prompt
        print(preview)
        print("-" * 50)
        print(f"📏 完整提示词长度: {len(prompt)} 字符")
        print()
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入系统模块: {e}")
        return False
    except Exception as e:
        print(f"❌ 预览失败: {e}")
        return False

def show_output_format():
    """
    显示输出格式说明
    """
    print("📄 输出文件格式:")
    print("   📊 JSON分析报告: business_analysis_YYYYMMDD_HHMMSS.json")
    print("   🗄️  数据库记录: augment_responses.db")
    print("   📋 日志文件: augment_monitor.log")
    print()
    
    print("📋 JSON报告结构:")
    print("   ├── analysis_type: 分析类型")
    print("   ├── project_info: 项目信息")
    print("   ├── request: 请求详情")
    print("   ├── response: AI分析结果")
    print("   └── metadata: 元数据信息")
    print()

def show_usage_instructions():
    """
    显示使用说明
    """
    print("🚀 使用方法:")
    print("   方法1: python start_augment_monitor.py")
    print("   方法2: python augment_monitor_system.py")
    print("   方法3: python install.py  # 安装并自动启动")
    print()
    
    print("⚠️  注意事项:")
    print("   • 确保Augment AI插件正常运行")
    print("   • 确保屏幕分辨率足够（推荐1920x1080+）")
    print("   • 确保Python有GUI自动化权限")
    print("   • 首次运行前请安装依赖: pip install -r requirements.txt")
    print()

def main():
    """
    主演示函数
    """
    show_system_info()
    show_analysis_scope()
    show_system_features()
    
    # 尝试显示提示词预览
    if demo_prompt_preview():
        print("✅ 系统模块加载正常")
    else:
        print("⚠️ 系统模块加载异常，请检查依赖")
    
    show_output_format()
    show_usage_instructions()
    
    print("=" * 70)
    print("🎉 演示完成！系统已准备就绪，可以开始自动业务分析")
    print("=" * 70)
    
    # 询问是否立即启动
    try:
        choice = input("\n是否立即启动业务分析系统？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是', '启动']:
            print("\n🚀 正在启动业务分析系统...")
            from augment_monitor_system import main as monitor_main
            monitor_main()
        else:
            print("👋 演示结束，您可以稍后手动启动系统")
    except KeyboardInterrupt:
        print("\n👋 用户中断演示")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请手动运行: python start_augment_monitor.py")

if __name__ == "__main__":
    main()
