# 🚀 智能项目分析器使用说明

## 📋 功能概述

智能项目分析器是一个自动化工具，能够：
- 🔍 **自动识别项目类型**（Java、Python、Node.js、Rust等）
- 💻 **自动打开IntelliJ IDEA**并加载指定项目
- 📊 **生成项目结构分析**
- 📝 **创建定制化的分析提示词**
- 🎯 **引导Augment进行深度分析**

## 🎯 使用方法

### 方法1：命令行直接指定项目路径
```bash
python smart_project_analyzer.py "D:\your-project-path"
```

### 方法2：使用批处理文件
```bash
analyze_project.bat "D:\your-project-path"
```

### 方法3：交互式输入
```bash
python smart_project_analyzer.py
# 然后输入项目路径
```

## 📁 支持的项目类型

| 项目类型 | 识别标志 | 特殊分析 |
|---------|---------|---------|
| Maven Java | `pom.xml` | Java架构模式 |
| Gradle Project | `build.gradle` | Gradle构建分析 |
| Node.js | `package.json` | npm依赖分析 |
| Python | `requirements.txt`, `setup.py` | Python包结构 |
| Rust | `Cargo.toml` | Cargo生态分析 |
| Go | `go.mod` | Go模块分析 |

## 🔧 系统要求

- Python 3.8+
- IntelliJ IDEA（可选，会自动查找）
- Windows系统（当前版本）

## 📊 输出文件

运行后会生成以下文件：

1. **`{项目名}_analysis_prompt.txt`** - 定制化分析提示词
2. **`{项目名}_analysis_report.json`** - Augment生成的分析报告（需要手动执行）

## 🎯 完整工作流程

### 第1步：运行分析器
```bash
python smart_project_analyzer.py "D:\my-project"
```

### 第2步：系统自动执行
- ✅ 验证项目路径
- 📊 分析项目结构（文件数量、类型、主要文件）
- 🔍 识别项目类型
- 💻 尝试打开IntelliJ IDEA
- 📝 生成定制化提示词

### 第3步：使用Augment分析
1. 在IDEA中打开Augment
2. 复制生成的提示词
3. 发送给Augment
4. Augment会自动创建JSON分析报告

## 📋 示例输出

### 项目结构分析示例：
```
✅ 项目分析完成:
   项目类型: Python Project
   文件数量: 127
   目录数量: 12
   主要文件类型: ['.py', '.json', '.md', '.txt', '.bat']
```

### 生成的提示词示例：
```
请详细分析这个Python Project项目 "AI-assisted" 的业务流程和技术架构...

**项目基本信息：**
- 项目名称: AI-assisted
- 项目类型: Python Project
- 文件数量: 127
- 主要文件类型: .py, .json, .md, .txt, .bat

**重要要求：**
1. 请将所有分析结果组织成结构化的JSON格式
2. 请使用save-file工具将JSON数据保存到文件 `AI-assisted_analysis_report.json` 中
...
```

## 🛠️ 高级配置

### 自定义IDEA路径
如果系统无法自动找到IDEA，可以修改 `smart_project_analyzer.py` 中的 `idea_paths` 列表：

```python
self.idea_paths = [
    "你的IDEA安装路径\\bin\\idea64.exe",
    # 添加更多路径...
]
```

### 自定义项目类型识别
可以在 `analyze_project_structure` 方法中添加更多项目类型识别逻辑。

## ⚠️ 注意事项

1. **路径格式**：使用双引号包围包含空格的路径
2. **权限要求**：确保有读取项目目录的权限
3. **IDEA版本**：支持IntelliJ IDEA 2023.3及以上版本
4. **网络连接**：Augment需要网络连接进行分析

## 🔧 故障排除

### 问题1：找不到IDEA
```
❌ 未找到IntelliJ IDEA，请手动指定路径
```
**解决方案**：手动修改脚本中的IDEA路径，或手动打开IDEA

### 问题2：项目路径无效
```
❌ 路径不存在: D:\nonexistent-project
```
**解决方案**：检查路径是否正确，确保目录存在

### 问题3：权限不足
```
❌ 项目分析失败: Permission denied
```
**解决方案**：以管理员身份运行，或检查目录权限

## 🎉 使用示例

### 分析Java项目：
```bash
python smart_project_analyzer.py "D:\workspace\my-spring-boot-app"
```

### 分析Python项目：
```bash
python smart_project_analyzer.py "D:\projects\my-django-app"
```

### 分析Node.js项目：
```bash
python smart_project_analyzer.py "D:\dev\my-react-app"
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 项目路径是否正确
3. 是否有足够的系统权限
4. IDEA是否正确安装

---

**🎯 目标效果**：一键启动 → 自动分析 → 智能提示 → 深度报告

让项目分析变得简单高效！🚀
